#!/usr/bin/env python3
"""
批量处理工具示例
演示如何使用 PocketFlow 创建批量数据处理工具
"""

from python.helpers.tool import Tool, Response
from python.helpers.pocketflow_adapter import AgentZeroLLMAdapter, YAMLParser
from pocketflow import Node, BatchNode
import asyncio
import json


class DataValidationNode(Node):
    """数据验证节点"""
    
    async def run_async(self, shared):
        input_data = shared.get("input_data", "")
        
        # 尝试解析输入数据
        try:
            if isinstance(input_data, str):
                # 尝试解析为 JSON
                if input_data.strip().startswith('[') or input_data.strip().startswith('{'):
                    data_list = json.loads(input_data)
                else:
                    # 按行分割
                    data_list = [line.strip() for line in input_data.split('\n') if line.strip()]
            elif isinstance(input_data, list):
                data_list = input_data
            else:
                data_list = [str(input_data)]
            
            shared["validated_data"] = data_list
            shared["total_items"] = len(data_list)
            
            print(f"✅ 数据验证完成: {len(data_list)} 项")
            
        except Exception as e:
            shared["validation_error"] = str(e)
            print(f"❌ 数据验证失败: {e}")


class BatchProcessingNode(BatchNode):
    """批量处理节点"""
    
    def prep(self, shared):
        """准备批处理任务"""
        data_list = shared.get("validated_data", [])
        llm_adapter = shared.get("llm_adapter")
        processing_config = shared.get("processing_config", {})
        
        # 每个任务包含：数据项、LLM适配器、配置
        return [(item, llm_adapter, processing_config) for item in data_list]
    
    def exec(self, task_input):
        """执行单个处理任务"""
        item, llm_adapter, config = task_input
        
        try:
            # 这里使用同步方式调用（简化版本）
            # 实际项目中可能需要更复杂的异步处理
            
            processing_type = config.get("type", "summarize")
            
            if processing_type == "summarize":
                result = self._summarize_item(item)
            elif processing_type == "translate":
                result = self._translate_item(item, config.get("target_language", "英文"))
            elif processing_type == "analyze":
                result = self._analyze_item(item)
            else:
                result = self._default_process(item)
            
            return {
                "input": item,
                "output": result,
                "status": "success",
                "type": processing_type
            }
            
        except Exception as e:
            return {
                "input": item,
                "error": str(e),
                "status": "failed",
                "type": processing_type
            }
    
    def _summarize_item(self, item):
        """总结文本"""
        if len(str(item)) < 50:
            return f"简短内容：{item}"
        else:
            return f"长文本摘要：{str(item)[:100]}..."
    
    def _translate_item(self, item, target_lang):
        """翻译文本（模拟）"""
        return f"[翻译为{target_lang}] {item}"
    
    def _analyze_item(self, item):
        """分析文本"""
        word_count = len(str(item).split())
        char_count = len(str(item))
        return {
            "word_count": word_count,
            "char_count": char_count,
            "type": "text_analysis"
        }
    
    def _default_process(self, item):
        """默认处理"""
        return f"已处理：{item}"
    
    def post(self, shared, prep_res, exec_res_list):
        """处理批量结果"""
        successful = [r for r in exec_res_list if r.get("status") == "success"]
        failed = [r for r in exec_res_list if r.get("status") == "failed"]
        
        # 按处理类型分组
        by_type = {}
        for result in successful:
            proc_type = result.get("type", "unknown")
            if proc_type not in by_type:
                by_type[proc_type] = []
            by_type[proc_type].append(result)
        
        shared["batch_results"] = {
            "successful": successful,
            "failed": failed,
            "by_type": by_type,
            "total": len(exec_res_list),
            "success_rate": len(successful) / len(exec_res_list) if exec_res_list else 0
        }
        
        print(f"📊 批处理完成: {len(successful)}/{len(exec_res_list)} 成功")


class ResultFormattingNode(Node):
    """结果格式化节点"""
    
    async def run_async(self, shared):
        batch_results = shared.get("batch_results", {})
        processing_config = shared.get("processing_config", {})
        
        if not batch_results:
            shared["formatted_output"] = "❌ 没有处理结果"
            return
        
        # 生成详细报告
        report = self._generate_detailed_report(batch_results, processing_config)
        shared["formatted_output"] = report
        
        print("✅ 结果格式化完成")
    
    def _generate_detailed_report(self, results, config):
        """生成详细报告"""
        
        total = results.get("total", 0)
        successful = results.get("successful", [])
        failed = results.get("failed", [])
        success_rate = results.get("success_rate", 0)
        by_type = results.get("by_type", {})
        
        report = f"""
🎯 批量处理执行报告

📊 处理统计:
• 总计项目: {total}
• 成功处理: {len(successful)}
• 处理失败: {len(failed)}
• 成功率: {success_rate:.1%}

📋 处理类型分布:
"""
        
        for proc_type, items in by_type.items():
            report += f"• {proc_type}: {len(items)} 项\n"
        
        if successful:
            report += f"""
✅ 成功处理的项目:
"""
            for i, item in enumerate(successful[:5], 1):  # 只显示前5个
                input_text = str(item.get("input", ""))[:50]
                output_text = str(item.get("output", ""))[:50]
                report += f"{i}. 输入: {input_text}...\n"
                report += f"   输出: {output_text}...\n"
            
            if len(successful) > 5:
                report += f"   ... 还有 {len(successful) - 5} 项成功处理\n"
        
        if failed:
            report += f"""
❌ 处理失败的项目:
"""
            for i, item in enumerate(failed[:3], 1):  # 只显示前3个失败
                input_text = str(item.get("input", ""))[:50]
                error_text = str(item.get("error", ""))[:50]
                report += f"{i}. 输入: {input_text}...\n"
                report += f"   错误: {error_text}...\n"
            
            if len(failed) > 3:
                report += f"   ... 还有 {len(failed) - 3} 项处理失败\n"
        
        return report


class BatchProcessorTool(Tool):
    """
    批量处理工具
    支持多种处理类型：总结、翻译、分析等
    """
    
    async def execute(self, input_data="", processing_type="summarize", target_language="英文", **kwargs):
        """
        执行批量处理
        
        Args:
            input_data: 输入数据（JSON数组或换行分隔的文本）
            processing_type: 处理类型（summarize/translate/analyze）
            target_language: 目标语言（仅翻译时使用）
        """
        
        if not input_data:
            return Response(
                message="❌ 请提供需要批量处理的数据",
                break_loop=False
            )
        
        try:
            # 创建适配器
            llm_adapter = AgentZeroLLMAdapter(self.agent)
            
            # 处理配置
            processing_config = {
                "type": processing_type,
                "target_language": target_language,
                **kwargs
            }
            
            # 初始化共享状态
            shared = {
                "input_data": input_data,
                "llm_adapter": llm_adapter,
                "processing_config": processing_config,
                "start_time": asyncio.get_event_loop().time()
            }
            
            print(f"🚀 开始批量处理")
            print(f"📝 处理类型: {processing_type}")
            print(f"📊 输入数据: {str(input_data)[:100]}...")
            
            # 执行工作流
            await self._run_batch_workflow(shared)
            
            # 计算执行时间
            execution_time = asyncio.get_event_loop().time() - shared["start_time"]
            
            # 获取格式化结果
            result = shared.get("formatted_output", "处理完成")
            
            # 添加执行时间信息
            final_result = f"{result}\n\n⏱️ 总执行时间: {execution_time:.2f} 秒"
            
            return Response(
                message=final_result,
                break_loop=False
            )
            
        except Exception as e:
            return Response(
                message=f"❌ 批量处理失败: {str(e)}",
                break_loop=False
            )
    
    async def _run_batch_workflow(self, shared):
        """运行批量处理工作流"""
        
        # 步骤1：数据验证
        print("📥 步骤1: 数据验证")
        validation_node = DataValidationNode()
        await validation_node.run_async(shared)
        
        if "validation_error" in shared:
            shared["formatted_output"] = f"❌ 数据验证失败: {shared['validation_error']}"
            return
        
        # 步骤2：批量处理
        print("⚡ 步骤2: 批量处理")
        batch_node = BatchProcessingNode()
        
        # 执行批处理
        prep_result = batch_node.prep(shared)
        exec_results = []
        
        total_tasks = len(prep_result)
        for i, task in enumerate(prep_result, 1):
            print(f"   处理进度: {i}/{total_tasks}")
            result = batch_node.exec(task)
            exec_results.append(result)
        
        batch_node.post(shared, prep_result, exec_results)
        
        # 步骤3：结果格式化
        print("📊 步骤3: 结果格式化")
        formatting_node = ResultFormattingNode()
        await formatting_node.run_async(shared)


# 测试代码
if __name__ == "__main__":
    # 这里可以添加测试代码
    test_data = [
        "这是第一个测试文本",
        "这是第二个测试文本，内容稍微长一些",
        "第三个测试文本"
    ]
    
    print("批量处理工具示例")
    print(f"测试数据: {test_data}")

#!/usr/bin/env python3
"""
调试输出格式 - 查看实际的输出内容
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from python.tools.code_generator_tool import CodeGeneratorTool
from agent import Agent
from initialize import initialize_agent


async def debug_output():
    """调试输出格式"""
    
    print("🔍 === 调试输出格式 ===")
    print()
    
    # 初始化 Agent
    config = initialize_agent()
    agent = Agent(0, config)
    
    # 创建工具实例
    tool = CodeGeneratorTool(
        agent=agent,
        name="code_generator_tool",
        method="",
        args={},
        message=""
    )
    
    print("📝 执行测试...")
    
    try:
        result = await tool.execute(
            problem="实现简单的排序算法",
            num_examples=2,
            max_iterations=1
        )
        
        if result and hasattr(result, 'message'):
            message = result.message
            print("📋 完整输出内容:")
            print("=" * 80)
            print(repr(message))  # 使用 repr 显示原始字符串
            print("=" * 80)
            print()
            print("📋 格式化输出内容:")
            print("=" * 80)
            print(message)
            print("=" * 80)
            
        else:
            print("❌ 无返回结果")
            
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(debug_output())

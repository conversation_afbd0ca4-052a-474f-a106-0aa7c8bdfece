#!/usr/bin/env python3
"""
测试独立 PocketFlow 工作流
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from document_analyzer.workflow import DocumentAnalyzerWorkflow


async def simple_test():
    """简单测试"""
    print("🧪 测试独立 PocketFlow 工作流...")
    
    try:
        # 创建工作流实例
        workflow = DocumentAnalyzerWorkflow()
        
        # 测试文档
        test_document = """
        这是一个测试文档。人工智能技术正在快速发展，
        深度学习和机器学习在各个领域都有广泛应用。
        这项技术将会改变我们的生活和工作方式。
        """
        
        print("📝 测试文档摘要分析...")
        result = await workflow.analyze_document(test_document, "summary")
        
        if result["success"]:
            print("✅ 测试成功！")
            print("📊 结果预览:")
            print(result["output"][:300] + "...")
        else:
            print("❌ 测试失败")
            print(f"错误: {result.get('error', '未知错误')}")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    asyncio.run(simple_test())

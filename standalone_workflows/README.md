# PocketFlow 独立工作流项目

## 🎯 项目概述

这个项目演示了如何使用 PocketFlow 开发独立的 AI 工作流应用，同时借用 Agent Zero 的模型配置和 LLM 调用能力。

## ⚠️ 重要特性：完全独立运行

**独立工作流不需要 Agent Zero 服务运行！**

- ✅ **只需要**：Agent Zero 的代码库、配置文件、API 密钥
- ❌ **不需要**：Agent Zero 的 Web 服务、主进程、数据库连接

这意味着您可以：
- 在没有启动 Agent Zero 的情况下运行工作流
- 将工作流部署到独立的服务器
- 创建轻量级的 AI 应用服务
- 避免 Agent Zero Web 界面的资源开销

## 📁 项目结构

```
standalone_workflows/
├── README.md                           # 项目说明
├── run_document_analyzer.py            # 文档分析器启动脚本
├── test_standalone.py                  # 测试脚本
├── document_analyzer/                  # 文档分析器示例
│   ├── __init__.py
│   ├── config.py                       # Agent Zero 模型适配器
│   ├── workflow.py                     # PocketFlow 工作流定义
│   └── main.py                         # 主程序逻辑
└── [其他独立工作流项目]/
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保在 Agent Zero 的 conda 环境中
conda activate A0

# 安装必要依赖
pip install pocketflow pyyaml
```

### 2. 运行文档分析器示例

```bash
# 在 Agent Zero 项目根目录下
cd standalone_workflows

# 运行文档分析器
python run_document_analyzer.py

# 或者运行简单测试
python test_standalone.py
```

### 3. 查看帮助信息

```bash
python run_document_analyzer.py --help
```

## 📋 功能特性

### 文档分析器 (Document Analyzer)

- **全面分析**: 深度分析文档内容、结构和质量
- **摘要生成**: 自动生成文档摘要和关键要点
- **情感分析**: 分析文档的情感倾向和强度
- **关键词提取**: 提取文档的关键词和主题
- **批量处理**: 支持多个文档的批量分析
- **交互模式**: 实时输入文档进行分析

## 🏗️ 技术架构

```
独立 PocketFlow 应用
├── 业务逻辑层 (PocketFlow Nodes)
├── 模型适配层 (Agent Zero LLM Adapter)
├── 配置管理层 (Agent Zero Settings)
└── 基础设施层 (Agent Zero Models & API)
```

### 核心组件

1. **AgentZeroModelAdapter**: 模型适配器
   - 初始化 Agent Zero 实例
   - 提供统一的 LLM 调用接口
   - 管理模型配置信息

2. **PocketFlow Nodes**: 工作流节点
   - DocumentInputNode: 文档输入和预处理
   - DocumentAnalysisNode: 文档分析处理
   - ResultFormattingNode: 结果格式化输出

3. **Workflow**: 工作流编排
   - 定义节点连接关系
   - 管理共享状态
   - 控制执行流程

## 💡 使用示例

### 基本用法

```python
from document_analyzer.workflow import DocumentAnalyzerWorkflow

async def analyze_document():
    workflow = DocumentAnalyzerWorkflow()
    
    result = await workflow.analyze_document(
        document="你的文档内容",
        analysis_type="comprehensive"  # summary/sentiment/keywords/comprehensive
    )
    
    print(result["output"])
```

### 批量处理

```python
documents = ["文档1", "文档2", "文档3"]
batch_result = await workflow.batch_analyze(documents, "summary")
```

### 自定义分析类型

```python
# 支持的分析类型
analysis_types = [
    "comprehensive",  # 全面分析
    "summary",        # 摘要生成
    "sentiment",      # 情感分析
    "keywords"        # 关键词提取
]
```

## 🔧 自定义开发

### 创建新的独立工作流

1. **创建项目目录**:
```bash
mkdir my_workflow
cd my_workflow
touch __init__.py config.py workflow.py main.py
```

2. **复制模型适配器**:
```python
# 从 document_analyzer/config.py 复制 AgentZeroModelAdapter
```

3. **定义工作流节点**:
```python
from pocketflow import Node

class MyProcessingNode(Node):
    async def run_async(self, shared):
        # 你的处理逻辑
        pass
```

4. **创建工作流类**:
```python
class MyWorkflow:
    def __init__(self):
        self.model_adapter = AgentZeroModelAdapter()
        # 定义你的节点
    
    async def run(self, input_data):
        # 执行工作流
        pass
```

### 扩展现有工作流

```python
# 添加新的分析类型
class DocumentAnalysisNode(Node):
    async def run_async(self, shared):
        analysis_type = shared.get("analysis_type")
        
        if analysis_type == "my_custom_analysis":
            # 你的自定义分析逻辑
            pass
```

## 🎯 优势特点

1. **独立运行**: 不依赖 Agent Zero 的 Web 界面
2. **模型复用**: 借用 Agent Zero 的模型配置和管理
3. **灵活扩展**: 基于 PocketFlow 的模块化设计
4. **易于部署**: 可以打包为独立的应用程序
5. **高性能**: 支持异步处理和批量操作

## 📚 应用场景

- **文档处理服务**: 企业内部文档分析系统
- **内容审核工具**: 自动化内容质量检查
- **研究辅助工具**: 学术论文分析和摘要
- **客户反馈分析**: 用户评论情感分析
- **知识提取系统**: 从文档中提取结构化信息

## 🔍 故障排除

### 常见问题

1. **模型适配器初始化失败**
   - 检查 Agent Zero 项目路径是否正确
   - 确认 API 密钥配置

2. **LLM 调用失败**
   - 检查网络连接
   - 验证 API 密钥有效性

3. **依赖包缺失**
   - 运行 `pip install pocketflow pyyaml`

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查模型配置
workflow = DocumentAnalyzerWorkflow()
model_info = workflow.model_adapter.get_model_info()
print(model_info)
```

## 📖 相关文档

- [PocketFlow 独立工作流开发指南](../POCKETFLOW_STANDALONE_WORKFLOW_GUIDE.md)
- [PocketFlow 扩展开发指南](../POCKETFLOW_EXTENSION_GUIDE.md)
- [故障排除指南](../POCKETFLOW_TROUBLESHOOTING_GUIDE.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

本项目遵循与 Agent Zero 相同的许可证。

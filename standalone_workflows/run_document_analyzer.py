#!/usr/bin/env python3
"""
独立文档分析器启动脚本
使用 PocketFlow + Agent Zero 模型
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from document_analyzer.main import main


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    
    # 检查必要的包
    try:
        import pocketflow
        print("✅ PocketFlow 已安装")
    except ImportError:
        print("❌ PocketFlow 未安装，请运行: pip install pocketflow")
        return False
    
    try:
        import yaml
        print("✅ PyYAML 已安装")
    except ImportError:
        print("❌ PyYAML 未安装，请运行: pip install pyyaml")
        return False
    
    # 检查 Agent Zero 项目结构
    required_files = [
        "initialize.py",
        "agent.py",
        "python/helpers/settings.py"
    ]
    
    project_root = os.path.dirname(os.path.dirname(__file__))
    for file_path in required_files:
        full_path = os.path.join(project_root, file_path)
        if not os.path.exists(full_path):
            print(f"❌ 缺少 Agent Zero 文件: {file_path}")
            return False
    
    print("✅ Agent Zero 项目结构完整")
    
    # 检查环境变量（API 密钥）
    api_keys = [
        "API_KEY_OPENAI",
        "OPENAI_API_KEY",
        "API_KEY_DEEPSEEK",
        "API_KEY_SILICONFLOW"
    ]
    
    found_key = False
    for key in api_keys:
        if os.getenv(key):
            print(f"✅ 找到 API 密钥: {key}")
            found_key = True
            break
    
    if not found_key:
        print("⚠️ 未找到 API 密钥，请确保设置了相应的环境变量")
        print("   或在 Agent Zero 中配置了 API 密钥")
    
    return True


def show_usage():
    """显示使用说明"""
    print("""
🎯 独立文档分析器使用说明

📋 功能特性:
• 全面文档分析 - 深度分析文档内容、结构和质量
• 摘要生成 - 自动生成文档摘要和要点
• 情感分析 - 分析文档的情感倾向和强度
• 关键词提取 - 提取文档的关键词和主题
• 批量处理 - 支持多个文档的批量分析
• 交互模式 - 实时输入文档进行分析

🚀 运行方式:
1. 演示模式: python run_document_analyzer.py
2. 交互模式: 运行后选择进入交互模式

🔧 技术架构:
• PocketFlow: 工作流编排框架
• Agent Zero: 模型管理和 LLM 调用
• 独立运行: 不依赖 Agent Zero 的 Web 界面

📚 示例用途:
• 学术论文分析
• 商业文档审查
• 新闻文章摘要
• 用户评论情感分析
• 技术文档关键词提取
""")


if __name__ == "__main__":
    print("🎯 PocketFlow 独立文档分析器")
    print("="*60)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ["-h", "--help", "help"]:
            show_usage()
            sys.exit(0)
        elif sys.argv[1] in ["-v", "--version", "version"]:
            print("版本: 1.0.0")
            print("基于: PocketFlow + Agent Zero")
            sys.exit(0)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    print("\n🚀 启动独立文档分析器...")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

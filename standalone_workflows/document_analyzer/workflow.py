"""
Document Analyzer - PocketFlow 工作流
独立的文档分析工作流，借用 Agent Zero 的模型能力
"""

from pocketflow import Node, Flow
import asyncio
import yaml
import json
from .config import AgentZeroModelAdapter


class DocumentInputNode(Node):
    """文档输入和预处理节点"""
    
    def prep(self, shared):
        document = shared.get('document', '')
        print(f"📄 接收文档: {len(document)} 字符")
        return document
    
    def exec(self, document):
        # 文档预处理
        if not document or len(document.strip()) < 10:
            return {"error": "文档内容太短或为空"}
        
        # 基本统计
        lines = document.split('\n')
        words = document.split()
        
        return {
            "processed_document": document.strip(),
            "stats": {
                "characters": len(document),
                "lines": len(lines),
                "words": len(words),
                "paragraphs": len([line for line in lines if line.strip()])
            },
            "status": "success"
        }
    
    def post(self, shared, prep_res, exec_res):
        if exec_res.get("error"):
            shared["input_error"] = exec_res["error"]
        else:
            shared["document"] = exec_res["processed_document"]
            shared["document_stats"] = exec_res["stats"]
        print(f"✅ 文档预处理完成")


class DocumentAnalysisNode(Node):
    """文档分析节点"""
    
    async def run_async(self, shared):
        """异步执行文档分析"""
        if shared.get("input_error"):
            print("❌ 跳过文档分析：输入错误")
            return
        
        model_adapter = shared.get("model_adapter")
        document = shared.get("document", "")
        analysis_type = shared.get("analysis_type", "comprehensive")
        
        # 根据分析类型构建不同的提示
        if analysis_type == "summary":
            prompt = f"""
            请为以下文档生成摘要：

            {document}

            请提供：
            1. 核心内容摘要（100-200字）
            2. 主要观点（3-5个要点）
            3. 关键信息提取

            请以YAML格式返回结果：
            ```yaml
            summary: |
              文档摘要内容...
            
            key_points:
              - 要点1
              - 要点2
              - 要点3
            
            key_information:
              - 关键信息1
              - 关键信息2
            ```
            """
        elif analysis_type == "sentiment":
            prompt = f"""
            请分析以下文档的情感倾向：

            {document}

            请分析：
            1. 整体情感倾向（积极/中性/消极）
            2. 情感强度（1-10分）
            3. 具体情感词汇
            4. 情感分析依据

            请以YAML格式返回结果：
            ```yaml
            sentiment: "积极/中性/消极"
            intensity: 8
            emotional_words:
              - 词汇1
              - 词汇2
            analysis: |
              情感分析依据...
            ```
            """
        elif analysis_type == "keywords":
            prompt = f"""
            请提取以下文档的关键词和主题：

            {document}

            请提供：
            1. 关键词（10-15个）
            2. 主要主题（3-5个）
            3. 专业术语
            4. 重要概念

            请以YAML格式返回结果：
            ```yaml
            keywords:
              - 关键词1
              - 关键词2
            
            topics:
              - 主题1
              - 主题2
            
            technical_terms:
              - 术语1
              - 术语2
            
            concepts:
              - 概念1
              - 概念2
            ```
            """
        else:  # comprehensive
            prompt = f"""
            请对以下文档进行全面分析：

            {document}

            请提供：
            1. 文档类型和性质
            2. 核心内容摘要
            3. 主要观点和论据
            4. 关键信息提取
            5. 文档质量评估
            6. 改进建议

            请以YAML格式返回结果：
            ```yaml
            document_type: "文档类型"
            
            summary: |
              核心内容摘要...
            
            main_points:
              - 观点1
              - 观点2
            
            key_information:
              - 信息1
              - 信息2
            
            quality_assessment:
              clarity: 8
              completeness: 7
              accuracy: 9
              
            suggestions:
              - 建议1
              - 建议2
            ```
            """
        
        try:
            print(f"🧠 开始文档分析 ({analysis_type})...")
            response = await model_adapter.call_llm(
                prompt=prompt,
                system_prompt="你是一个专业的文档分析师，请提供准确、详细和有用的分析结果。"
            )
            
            shared["analysis_response"] = response
            shared["analysis_status"] = "success"
            print(f"✅ 文档分析完成")
            
        except Exception as e:
            shared["analysis_error"] = str(e)
            shared["analysis_status"] = "failed"
            print(f"❌ 文档分析失败: {e}")


class ResultFormattingNode(Node):
    """结果格式化节点"""
    
    def prep(self, shared):
        return {
            "analysis_response": shared.get("analysis_response", ""),
            "document_stats": shared.get("document_stats", {}),
            "analysis_type": shared.get("analysis_type", "unknown"),
            "analysis_status": shared.get("analysis_status", "unknown"),
            "model_info": shared.get("model_adapter").get_model_info() if shared.get("model_adapter") else {}
        }
    
    def exec(self, data):
        if data["analysis_status"] != "success":
            return {
                "formatted_output": f"❌ 分析失败，无法生成报告",
                "success": False
            }
        
        # 格式化输出
        stats = data["document_stats"]
        model_info = data["model_info"]
        
        output = f"""
📄 文档分析报告

📊 文档统计:
• 字符数: {stats.get('characters', 0)}
• 行数: {stats.get('lines', 0)}
• 词数: {stats.get('words', 0)}
• 段落数: {stats.get('paragraphs', 0)}

🔍 分析类型: {data['analysis_type']}

🧠 使用模型: {model_info.get('utility_model', {}).get('provider', 'Unknown')} - {model_info.get('utility_model', {}).get('name', 'Unknown')}

📋 分析结果:
{data['analysis_response']}

✅ 分析完成时间: {asyncio.get_event_loop().time()}
"""
        
        return {
            "formatted_output": output,
            "success": True,
            "raw_analysis": data['analysis_response'],
            "stats": stats
        }
    
    def post(self, shared, prep_res, exec_res):
        shared["final_output"] = exec_res["formatted_output"]
        shared["workflow_success"] = exec_res["success"]
        shared["raw_result"] = exec_res.get("raw_analysis", "")
        print(f"📊 结果格式化完成")


class DocumentAnalyzerWorkflow:
    """文档分析工作流"""
    
    def __init__(self):
        self.model_adapter = AgentZeroModelAdapter()
        self.input_node = DocumentInputNode()
        self.analysis_node = DocumentAnalysisNode()
        self.output_node = ResultFormattingNode()
    
    async def analyze_document(self, document: str, analysis_type: str = "comprehensive"):
        """
        分析文档
        
        Args:
            document: 文档内容
            analysis_type: 分析类型 (summary/sentiment/keywords/comprehensive)
        
        Returns:
            分析结果
        """
        print(f"🚀 启动文档分析工作流")
        print(f"📝 分析类型: {analysis_type}")
        
        # 初始化共享状态
        shared = {
            "document": document,
            "analysis_type": analysis_type,
            "model_adapter": self.model_adapter
        }
        
        try:
            # 手动执行节点
            # 1. 文档输入处理
            print("📄 步骤1: 文档预处理")
            prep_res = self.input_node.prep(shared)
            exec_res = self.input_node.exec(prep_res)
            self.input_node.post(shared, prep_res, exec_res)
            
            # 2. 文档分析
            print("🧠 步骤2: 文档分析")
            await self.analysis_node.run_async(shared)
            
            # 3. 结果格式化
            print("📊 步骤3: 结果格式化")
            prep_res = self.output_node.prep(shared)
            exec_res = self.output_node.exec(prep_res)
            self.output_node.post(shared, prep_res, exec_res)
            
            return {
                "success": shared.get("workflow_success", False),
                "output": shared.get("final_output", ""),
                "raw_analysis": shared.get("raw_result", ""),
                "model_info": self.model_adapter.get_model_info()
            }
            
        except Exception as e:
            print(f"❌ 工作流执行失败: {e}")
            return {
                "success": False,
                "output": f"文档分析失败: {str(e)}",
                "error": str(e)
            }
    
    async def batch_analyze(self, documents: list, analysis_type: str = "comprehensive"):
        """批量分析文档"""
        print(f"📚 开始批量文档分析: {len(documents)} 个文档")
        
        results = []
        for i, doc in enumerate(documents, 1):
            print(f"\n📄 处理文档 {i}/{len(documents)}")
            result = await self.analyze_document(doc, analysis_type)
            results.append({
                "document_index": i,
                "result": result
            })
        
        return {
            "batch_results": results,
            "total_documents": len(documents),
            "successful": len([r for r in results if r["result"]["success"]])
        }

"""
Document Analyzer - 主程序
独立的文档分析应用示例
"""

import asyncio
import sys
from .workflow import DocumentAnalyzerWorkflow


async def demo_comprehensive_analysis():
    """演示全面分析"""
    print("📋 演示1: 全面文档分析")
    
    sample_document = """
    人工智能技术发展报告

    随着深度学习和机器学习技术的快速发展，人工智能已经成为当今科技领域最重要的趋势之一。
    从自然语言处理到计算机视觉，从自动驾驶到智能推荐系统，AI技术正在深刻改变着我们的生活和工作方式。

    在自然语言处理领域，大型语言模型如GPT、BERT等的出现，使得机器能够更好地理解和生成人类语言。
    这些模型在文本分类、情感分析、机器翻译等任务上取得了显著的性能提升。

    计算机视觉技术也取得了重大突破。卷积神经网络（CNN）的发展使得图像识别、目标检测、
    图像分割等任务的准确率大幅提升。这些技术在医疗诊断、自动驾驶、安防监控等领域有着广泛的应用。

    然而，AI技术的发展也面临着一些挑战。数据隐私、算法偏见、技术伦理等问题需要我们认真对待。
    同时，AI技术的快速发展也对传统行业和就业市场带来了冲击，需要社会各界共同应对。

    展望未来，人工智能技术将继续快速发展，并在更多领域发挥重要作用。
    我们需要在推动技术进步的同时，也要关注其社会影响，确保AI技术能够造福人类。
    """
    
    workflow = DocumentAnalyzerWorkflow()
    result = await workflow.analyze_document(sample_document, "comprehensive")
    
    print("="*60)
    print(result["output"])
    return result


async def demo_sentiment_analysis():
    """演示情感分析"""
    print("\n📋 演示2: 情感分析")
    
    sample_review = """
    这款产品真的让我非常失望。包装破损，产品质量也很差，完全不值这个价格。
    客服态度也很糟糕，根本不解决问题。我强烈不推荐购买这款产品，
    简直是浪费金钱和时间。希望厂家能够改进产品质量和服务态度。
    """
    
    workflow = DocumentAnalyzerWorkflow()
    result = await workflow.analyze_document(sample_review, "sentiment")
    
    print("="*60)
    print(result["output"])
    return result


async def demo_keyword_extraction():
    """演示关键词提取"""
    print("\n📋 演示3: 关键词提取")
    
    sample_article = """
    区块链技术是一种分布式账本技术，通过密码学方法确保数据的安全性和不可篡改性。
    比特币是区块链技术的第一个成功应用，它实现了去中心化的数字货币系统。
    
    智能合约是区块链技术的重要组成部分，它允许在区块链上执行自动化的合约条款。
    以太坊平台是智能合约的主要实现平台，支持多种去中心化应用（DApp）的开发。
    
    去中心化金融（DeFi）是区块链技术在金融领域的重要应用，它通过智能合约
    实现了无需传统金融中介的金融服务，包括借贷、交易、保险等。
    
    NFT（非同质化代币）是另一个热门的区块链应用，它为数字艺术品、
    游戏道具等数字资产提供了唯一性证明和所有权确认。
    """
    
    workflow = DocumentAnalyzerWorkflow()
    result = await workflow.analyze_document(sample_article, "keywords")
    
    print("="*60)
    print(result["output"])
    return result


async def demo_batch_analysis():
    """演示批量分析"""
    print("\n📋 演示4: 批量文档分析")
    
    documents = [
        "今天天气很好，阳光明媚，适合外出游玩。心情也变得愉快起来。",
        "这次会议讨论了公司的发展战略，包括市场扩张、产品创新和人才培养等重要议题。",
        "机器学习算法在数据挖掘中发挥着重要作用，特别是在模式识别和预测分析方面。"
    ]
    
    workflow = DocumentAnalyzerWorkflow()
    batch_result = await workflow.batch_analyze(documents, "summary")
    
    print("="*60)
    print(f"📚 批量分析完成:")
    print(f"• 总文档数: {batch_result['total_documents']}")
    print(f"• 成功分析: {batch_result['successful']}")
    
    for item in batch_result["batch_results"]:
        print(f"\n📄 文档 {item['document_index']}:")
        if item["result"]["success"]:
            print("✅ 分析成功")
            # 显示简化结果
            raw_analysis = item["result"]["raw_analysis"]
            print(f"分析结果: {raw_analysis[:200]}...")
        else:
            print("❌ 分析失败")
    
    return batch_result


async def interactive_mode():
    """交互模式"""
    print("\n🎮 交互模式")
    print("请输入要分析的文档内容（输入 'quit' 退出）:")
    
    workflow = DocumentAnalyzerWorkflow()
    
    while True:
        try:
            print("\n" + "="*40)
            document = input("📝 请输入文档内容: ")
            
            if document.lower() in ['quit', 'exit', 'q']:
                print("👋 退出交互模式")
                break
            
            if len(document.strip()) < 10:
                print("⚠️ 文档内容太短，请输入至少10个字符")
                continue
            
            print("\n📋 选择分析类型:")
            print("1. 全面分析 (comprehensive)")
            print("2. 摘要生成 (summary)")
            print("3. 情感分析 (sentiment)")
            print("4. 关键词提取 (keywords)")
            
            choice = input("请选择 (1-4): ").strip()
            
            analysis_types = {
                "1": "comprehensive",
                "2": "summary", 
                "3": "sentiment",
                "4": "keywords"
            }
            
            analysis_type = analysis_types.get(choice, "comprehensive")
            
            print(f"\n🚀 开始分析...")
            result = await workflow.analyze_document(document, analysis_type)
            
            print("\n" + "="*60)
            print(result["output"])
            
        except KeyboardInterrupt:
            print("\n👋 退出交互模式")
            break
        except Exception as e:
            print(f"❌ 分析出错: {e}")


async def main():
    """主程序入口"""
    print("🎯 PocketFlow 独立文档分析工作流")
    print("="*60)
    print("📚 这是一个独立的文档分析应用，借用 Agent Zero 的模型能力")
    
    try:
        # 运行演示
        await demo_comprehensive_analysis()
        await demo_sentiment_analysis()
        await demo_keyword_extraction()
        await demo_batch_analysis()
        
        # 交互模式
        print("\n🎮 是否进入交互模式？(y/n): ", end="")
        if input().lower() in ['y', 'yes']:
            await interactive_mode()
        
        print("\n🎉 演示完成！")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())

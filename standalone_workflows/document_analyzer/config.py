"""
Document Analyzer - 配置模块
借用 Agent Zero 的模型配置
"""

import sys
import os

# 添加 Agent Zero 项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from initialize import initialize_agent
from agent import Agent
from python.helpers import settings


class AgentZeroModelAdapter:
    """Agent Zero 模型适配器，用于独立工作流"""
    
    def __init__(self):
        self.agent = None
        self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化 Agent Zero 实例"""
        try:
            # 使用 Agent Zero 的配置初始化
            config = initialize_agent()
            self.agent = Agent(0, config)
            
            # 获取当前模型配置信息
            current_settings = settings.get_settings()
            self.chat_model_info = {
                'provider': current_settings['chat_model_provider'],
                'name': current_settings['chat_model_name']
            }
            self.utility_model_info = {
                'provider': current_settings['util_model_provider'],
                'name': current_settings['util_model_name']
            }
            
            print(f"✅ 模型适配器初始化成功")
            print(f"📋 Chat Model: {self.chat_model_info['provider']} - {self.chat_model_info['name']}")
            print(f"📋 Utility Model: {self.utility_model_info['provider']} - {self.utility_model_info['name']}")
            
        except Exception as e:
            print(f"❌ 模型适配器初始化失败: {e}")
            raise
    
    async def call_llm(self, prompt: str, system_prompt: str = "", use_chat_model: bool = False) -> str:
        """
        调用 LLM 模型
        
        Args:
            prompt: 用户提示
            system_prompt: 系统提示
            use_chat_model: 是否使用 chat_model（默认使用 utility_model）
        
        Returns:
            LLM 响应文本
        """
        try:
            if use_chat_model:
                # 使用 chat_model（更适合对话）
                from langchain_core.prompts import ChatPromptTemplate
                from langchain_core.messages import HumanMessage, SystemMessage
                
                messages = []
                if system_prompt:
                    messages.append(SystemMessage(content=system_prompt))
                messages.append(HumanMessage(content=prompt))
                
                chat_prompt = ChatPromptTemplate.from_messages(messages)
                response = await self.agent.call_chat_model(chat_prompt)
            else:
                # 使用 utility_model（推荐，更稳定）
                if not system_prompt:
                    system_prompt = "你是一个专业的文档分析师，请提供准确和有用的分析。"
                
                response = await self.agent.call_utility_model(
                    system=system_prompt,
                    message=prompt
                )
            
            return response
            
        except Exception as e:
            print(f"❌ LLM 调用失败: {e}")
            return f"LLM调用错误: {str(e)}"
    
    def get_model_info(self):
        """获取当前模型配置信息"""
        return {
            'chat_model': self.chat_model_info,
            'utility_model': self.utility_model_info
        }

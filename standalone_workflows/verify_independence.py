#!/usr/bin/env python3
"""
验证独立工作流的独立性
证明不需要 Agent Zero 服务运行
"""

import asyncio
import sys
import os
import psutil
import subprocess

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))


def check_agent_zero_processes():
    """检查是否有 Agent Zero 相关进程在运行"""
    agent_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            
            # 检查可能的 Agent Zero 进程
            if any(keyword in cmdline.lower() for keyword in [
                'agent-zero', 'agent_zero', 'start_agent', 'webui', 'gradio'
            ]):
                agent_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cmdline': cmdline
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return agent_processes


def check_network_ports():
    """检查 Agent Zero 常用端口是否被占用"""
    common_ports = [7860, 8080, 8888, 3000, 5000]  # Agent Zero 可能使用的端口
    occupied_ports = []
    
    for port in common_ports:
        try:
            connections = psutil.net_connections()
            for conn in connections:
                if conn.laddr.port == port and conn.status == 'LISTEN':
                    occupied_ports.append(port)
                    break
        except:
            continue
    
    return occupied_ports


async def test_independent_execution():
    """测试独立执行能力"""
    print("🧪 测试独立工作流执行...")
    
    try:
        # 导入并测试独立工作流
        from document_analyzer.config import AgentZeroModelAdapter
        
        print("✅ 成功导入 AgentZeroModelAdapter")
        
        # 尝试初始化模型适配器
        adapter = AgentZeroModelAdapter()
        print("✅ 成功初始化模型适配器")
        
        # 获取模型信息
        model_info = adapter.get_model_info()
        print(f"✅ 成功获取模型信息: {model_info}")
        
        # 尝试简单的 LLM 调用
        response = await adapter.call_llm(
            prompt="请回答：1+1等于多少？",
            system_prompt="你是一个数学助手"
        )
        print(f"✅ 成功调用 LLM: {response[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_document_analyzer():
    """测试文档分析器"""
    print("\n📄 测试文档分析器...")
    
    try:
        from document_analyzer.workflow import DocumentAnalyzerWorkflow
        
        workflow = DocumentAnalyzerWorkflow()
        print("✅ 成功创建文档分析工作流")
        
        # 简单测试
        test_doc = "这是一个测试文档，用于验证独立工作流的功能。"
        result = await workflow.analyze_document(test_doc, "summary")
        
        if result["success"]:
            print("✅ 文档分析成功")
            print(f"📊 结果长度: {len(result['output'])} 字符")
            return True
        else:
            print(f"❌ 文档分析失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 文档分析器测试失败: {e}")
        return False


def check_file_dependencies():
    """检查文件依赖"""
    print("\n📁 检查文件依赖...")
    
    required_files = [
        "initialize.py",
        "agent.py", 
        "python/helpers/settings.py",
        "models/__init__.py"
    ]
    
    project_root = os.path.dirname(os.path.dirname(__file__))
    missing_files = []
    
    for file_path in required_files:
        full_path = os.path.join(project_root, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0


def check_environment_variables():
    """检查环境变量"""
    print("\n🔑 检查环境变量...")
    
    api_keys = [
        "API_KEY_OPENAI",
        "OPENAI_API_KEY", 
        "API_KEY_DEEPSEEK",
        "API_KEY_SILICONFLOW",
        "API_KEY_VOLCENGINE"
    ]
    
    found_keys = []
    for key in api_keys:
        if os.getenv(key):
            print(f"✅ {key}")
            found_keys.append(key)
        else:
            print(f"❌ {key}")
    
    return len(found_keys) > 0


async def main():
    """主测试函数"""
    print("🎯 验证 PocketFlow 独立工作流的独立性")
    print("="*60)
    
    # 1. 检查 Agent Zero 进程
    print("🔍 检查 Agent Zero 相关进程...")
    agent_processes = check_agent_zero_processes()
    
    if agent_processes:
        print("⚠️ 发现 Agent Zero 相关进程:")
        for proc in agent_processes:
            print(f"   PID {proc['pid']}: {proc['name']} - {proc['cmdline'][:100]}...")
        print("   这可能影响独立性测试结果")
    else:
        print("✅ 未发现 Agent Zero 相关进程")
    
    # 2. 检查网络端口
    print("\n🌐 检查网络端口占用...")
    occupied_ports = check_network_ports()
    
    if occupied_ports:
        print(f"⚠️ 发现占用的端口: {occupied_ports}")
        print("   这些可能是 Agent Zero 服务端口")
    else:
        print("✅ 未发现 Agent Zero 常用端口被占用")
    
    # 3. 检查文件依赖
    files_ok = check_file_dependencies()
    
    # 4. 检查环境变量
    env_ok = check_environment_variables()
    
    # 5. 测试独立执行
    if files_ok and env_ok:
        print("\n🚀 开始独立性测试...")
        
        # 基础功能测试
        basic_test = await test_independent_execution()
        
        # 文档分析器测试
        analyzer_test = await test_document_analyzer()
        
        # 总结
        print("\n" + "="*60)
        print("📊 测试结果总结:")
        print(f"• Agent Zero 进程: {'运行中' if agent_processes else '未运行'}")
        print(f"• 网络端口: {'占用' if occupied_ports else '未占用'}")
        print(f"• 文件依赖: {'✅ 完整' if files_ok else '❌ 缺失'}")
        print(f"• 环境变量: {'✅ 配置' if env_ok else '❌ 未配置'}")
        print(f"• 基础功能: {'✅ 正常' if basic_test else '❌ 失败'}")
        print(f"• 文档分析: {'✅ 正常' if analyzer_test else '❌ 失败'}")
        
        if basic_test and analyzer_test:
            print("\n🎉 结论: 独立工作流可以在没有 Agent Zero 服务的情况下正常运行！")
            if agent_processes:
                print("   (即使检测到 Agent Zero 进程，独立工作流仍然可以独立运行)")
        else:
            print("\n❌ 结论: 独立工作流存在问题，需要检查配置")
    else:
        print("\n❌ 环境检查失败，无法进行独立性测试")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()

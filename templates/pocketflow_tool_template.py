# PocketFlow 工具模板
# 复制此文件并根据需要修改

from python.helpers.tool import Tool, Response
from python.helpers.pocketflow_adapter import AgentZeroLLMAdapter, YAMLParser
from pocketflow import Node, Flow, BatchNode
import asyncio


class TemplateNode(Node):
    """模板节点 - 根据需要修改"""
    
    async def run_async(self, shared):
        """异步执行节点逻辑"""
        
        # 获取输入数据
        input_data = shared.get("input_data", "")
        llm_adapter = shared.get("llm_adapter")
        
        # TODO: 实现您的处理逻辑
        print(f"🔄 处理数据: {input_data[:50]}...")
        
        try:
            # 示例：调用 LLM 处理
            prompt = f"""
            请处理以下数据：
            {input_data}
            
            处理要求：
            1. [您的要求1]
            2. [您的要求2]
            3. [您的要求3]
            
            请以YAML格式返回结果：
            ```yaml
            result: |
              处理结果...
            
            summary: |
              结果摘要...
            
            metadata:
              processed_items: 数量
              processing_time: 时间
            ```
            """
            
            response = await llm_adapter.call_llm(prompt)
            
            # 解析 YAML 响应
            yaml_str = YAMLParser.extract_yaml_from_response(response)
            result = YAMLParser.safe_load(yaml_str)
            
            # 保存结果到共享状态
            shared["node_result"] = result
            
            print(f"✅ 节点处理完成")
            
        except Exception as e:
            shared["node_result"] = {"error": str(e)}
            print(f"❌ 节点处理失败: {e}")


class TemplateBatchNode(BatchNode):
    """批处理节点模板"""
    
    def prep(self, shared):
        """准备批处理任务"""
        data_list = shared.get("data_list", [])
        config = shared.get("config", {})
        
        # 返回任务列表：每个元素是一个任务的输入
        return [(item, config) for item in data_list]
    
    def exec(self, task_input):
        """执行单个任务"""
        item, config = task_input
        
        # TODO: 实现单个任务的处理逻辑
        try:
            # 示例处理
            result = {
                "input": item,
                "output": f"处理后的{item}",
                "status": "success"
            }
            return result
        except Exception as e:
            return {
                "input": item,
                "error": str(e),
                "status": "failed"
            }
    
    def post(self, shared, prep_res, exec_res_list):
        """处理批处理结果"""
        successful = [r for r in exec_res_list if r.get("status") == "success"]
        failed = [r for r in exec_res_list if r.get("status") == "failed"]
        
        shared["batch_results"] = {
            "successful": successful,
            "failed": failed,
            "total": len(exec_res_list),
            "success_rate": len(successful) / len(exec_res_list) if exec_res_list else 0
        }
        
        print(f"📊 批处理完成: {len(successful)}/{len(exec_res_list)} 成功")


class TemplateWorkflowTool(Tool):
    """
    PocketFlow 工具模板
    
    使用说明：
    1. 复制此文件到 python/tools/your_tool_name.py
    2. 修改类名为 YourToolName
    3. 实现具体的业务逻辑
    4. 创建对应的系统提示文件
    5. 注册到工具系统
    """
    
    async def execute(self, input_data="", **kwargs):
        """
        工具执行入口
        
        Args:
            input_data: 输入数据
            **kwargs: 其他参数
        
        Returns:
            Response: 工具执行结果
        """
        
        # 参数验证
        if not input_data:
            return Response(
                message="❌ 请提供输入数据",
                break_loop=False
            )
        
        try:
            # 创建适配器
            llm_adapter = AgentZeroLLMAdapter(self.agent)
            
            # 初始化共享状态
            shared = {
                "input_data": input_data,
                "llm_adapter": llm_adapter,
                "tool_args": kwargs,
                "start_time": asyncio.get_event_loop().time()
            }
            
            print(f"🚀 开始执行工作流: {self.name}")
            print(f"📝 输入数据: {str(input_data)[:100]}...")
            
            # 运行工作流
            result = await self._run_workflow(shared)
            
            # 计算执行时间
            execution_time = asyncio.get_event_loop().time() - shared["start_time"]
            
            # 生成最终报告
            final_report = self._generate_report(shared, execution_time)
            
            return Response(
                message=final_report,
                break_loop=False
            )
            
        except Exception as e:
            return Response(
                message=f"❌ 工作流执行失败: {str(e)}",
                break_loop=False
            )
    
    async def _run_workflow(self, shared):
        """运行工作流的核心逻辑"""

        # ✅ 推荐方式：简单顺序执行（避免超时问题）
        await self._run_sequential_workflow(shared)

        # ❌ 避免：复杂的 PocketFlow Flow 调用（可能导致超时）
        # flow = self._create_pocketflow_workflow()
        # await self._run_pocketflow_workflow(flow, shared)

        return shared.get("final_result", "工作流执行完成")
    
    async def _run_sequential_workflow(self, shared):
        """顺序执行工作流节点（推荐方式，避免超时问题）"""

        # 步骤1：输入处理
        print("📥 步骤1: 输入处理")
        await self._process_input(shared)

        # 步骤2：核心处理
        print("💻 步骤2: 核心处理")
        await self._process_core_logic(shared)

        # 步骤3：结果整理
        print("📊 步骤3: 结果整理")
        await self._finalize_results(shared)

    async def _process_input(self, shared):
        """处理输入数据"""
        input_data = shared.get("input_data", "")
        llm_adapter = shared.get("llm_adapter")

        # TODO: 实现输入处理逻辑
        print(f"🔄 处理输入: {str(input_data)[:50]}...")

        # 示例：简单的数据验证
        if not input_data:
            shared["input_error"] = "输入数据为空"
            return

        shared["processed_input"] = input_data
        print("✅ 输入处理完成")

    async def _process_core_logic(self, shared):
        """核心处理逻辑"""
        llm_adapter = shared.get("llm_adapter")
        processed_input = shared.get("processed_input", "")

        if shared.get("input_error"):
            print("❌ 跳过核心处理：输入错误")
            return

        try:
            # ✅ 推荐：使用简化的 LLM 调用
            prompt = f"""
            请处理以下数据：
            {processed_input}

            处理要求：
            1. 分析数据内容
            2. 提取关键信息
            3. 生成处理结果

            请以简洁的格式返回结果。
            """

            # 直接调用，避免复杂的异步链
            response = await self.agent.call_utility_model(
                system="你是一个有用的AI助手，请根据用户的要求提供准确的回答。",
                message=prompt
            )

            shared["core_result"] = response
            print("✅ 核心处理完成")

        except Exception as e:
            shared["core_error"] = str(e)
            print(f"❌ 核心处理失败: {e}")
    
    def _create_pocketflow_workflow(self):
        """创建 PocketFlow 工作流对象（可选使用）"""
        
        # 创建节点
        node1 = TemplateNode()
        node2 = TemplateNode()
        
        # 定义流程
        node1 >> node2
        
        # 创建流程
        flow = Flow(start=node1)
        return flow
    
    async def _run_pocketflow_workflow(self, flow, shared):
        """运行 PocketFlow 工作流（需要适配同步接口）"""
        
        # 注意：PocketFlow 的 Flow.run() 是同步的
        # 这里需要适配为异步执行
        
        # 简化版本：手动执行节点
        # 实际使用中可能需要更复杂的适配逻辑
        pass
    
    async def _finalize_results(self, shared):
        """整理最终结果"""
        
        node_result = shared.get("node_result", {})
        batch_results = shared.get("batch_results", {})
        
        # TODO: 根据您的需求整理结果
        final_result = {
            "processing_result": node_result,
            "batch_summary": batch_results,
            "status": "completed"
        }
        
        shared["final_result"] = final_result
    
    def _generate_report(self, shared, execution_time):
        """生成执行报告"""
        
        final_result = shared.get("final_result", {})
        
        # TODO: 根据您的需求定制报告格式
        report = f"""
🎯 工作流执行报告

⏱️ 执行时间: {execution_time:.2f} 秒

📊 处理结果:
{self._format_results(final_result)}

✅ 执行状态: {final_result.get('status', '未知')}
"""
        
        return report
    
    def _format_results(self, results):
        """格式化结果显示"""
        
        if not results:
            return "无结果数据"
        
        # TODO: 根据您的数据结构定制格式化逻辑
        formatted = ""
        
        if "processing_result" in results:
            formatted += f"• 处理结果: {results['processing_result']}\n"
        
        if "batch_summary" in results:
            batch = results["batch_summary"]
            if isinstance(batch, dict):
                formatted += f"• 批处理: {batch.get('total', 0)} 项，成功率 {batch.get('success_rate', 0):.1%}\n"
        
        return formatted or "结果格式化失败"


# 使用示例
if __name__ == "__main__":
    # 这里可以添加测试代码
    print("PocketFlow 工具模板")
    print("请复制此文件并根据需要修改")

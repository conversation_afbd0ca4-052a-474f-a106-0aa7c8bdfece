## [Your Tool Name]

[工具的简短描述 - 一句话说明工具的主要功能]

**功能特性**：
- 🔄 **[特性1]**：[详细描述]
- 🧠 **[特性2]**：[详细描述]
- 📊 **[特性3]**：[详细描述]
- ⚡ **[特性4]**：[详细描述]

**使用方法**：
```json
{
  "tool_name": "[your_tool_name]",
  "parameters": {
    "input_data": "[必需参数描述]",
    "option1": "[可选参数1描述]",
    "option2": "[可选参数2描述]"
  }
}
```

**参数说明**：
- `input_data` (必需): [详细说明这个参数的作用、格式要求、示例]
- `option1` (可选): [参数说明，默认值]
- `option2` (可选): [参数说明，默认值]

**适用场景**：
- 📋 **[场景1]**：[具体应用描述]
- 🔍 **[场景2]**：[具体应用描述]
- ✍️ **[场景3]**：[具体应用描述]
- 🤖 **[场景4]**：[具体应用描述]

**工作流程**：
1. **[步骤1]**：[详细描述第一步做什么]
2. **[步骤2]**：[详细描述第二步做什么]
3. **[步骤3]**：[详细描述第三步做什么]
4. **[步骤4]**：[详细描述最后一步做什么]

**示例用法**：

*简单示例*：
```
请使用[工具名称]处理以下数据：
[示例输入数据]
```

*复杂示例*：
```
请使用[工具名称]处理以下数据，要求：
1. [具体要求1]
2. [具体要求2]
3. [具体要求3]

数据内容：
[示例输入数据]
```

**输出示例**：
```
🎯 [工具名称]执行报告

⏱️ 执行时间: 2.5 秒

📊 处理结果:
• [结果项1]: [具体内容]
• [结果项2]: [具体内容]
• [结果项3]: [具体内容]

📋 详细信息:
[详细的处理结果展示]

✅ 执行状态: 完成
```

**注意事项**：
- ⚠️ **[重要提醒1]**：[具体说明]
- ⚠️ **[重要提醒2]**：[具体说明]
- 💡 **[使用技巧]**：[具体建议]
- 🔧 **[故障排除]**：[常见问题解决方案]

**性能指标**：
- 处理速度：[具体数据，如每秒处理多少项]
- 准确率：[如果适用，说明准确率范围]
- 支持规模：[最大处理数据量]
- 并发能力：[是否支持并行处理]

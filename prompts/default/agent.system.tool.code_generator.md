## Code Generator Tool

智能代码生成工具，基于 PocketFlow 框架构建，能够自动生成测试用例、实现代码解决方案并迭代改进。

**功能特性**：
- 🧪 **自动测试用例生成**：从问题描述创建全面的测试用例
- 💻 **智能代码实现**：生成符合要求的函数代码
- 🔄 **迭代改进**：分析失败并自动修正代码
- 📊 **详细报告**：提供完整的生成过程和结果分析

**使用方法**：
```json
{
  "tool_name": "code_generator_tool",
  "parameters": {
    "problem": "编程问题描述（如LeetCode风格的问题）",
    "max_iterations": 3,
    "num_examples": 1
  }
}
```

**参数说明**：
- `problem` (必需): 编程问题的详细描述，包括输入输出要求和示例
- `max_iterations` (可选): 最大迭代次数，默认为3次
- `num_examples` (可选): 生成测试用例的数量，默认为1个，范围1-10个

**适用场景**：
- 🎯 **算法问题求解**：LeetCode、HackerRank 等编程题
- 🔧 **函数实现**：根据需求自动生成函数代码
- 🧪 **测试驱动开发**：先生成测试用例再实现代码
- 📚 **学习辅助**：理解问题解决思路和测试策略

**工作流程**：
1. **分析问题**：理解问题需求和约束条件
2. **生成测试用例**：创建包括边界情况的全面测试
3. **实现代码**：编写满足测试要求的函数
4. **执行测试**：运行所有测试用例验证正确性
5. **迭代改进**：分析失败原因并修正代码
6. **生成报告**：提供详细的结果分析

**示例用法**：

```
请使用代码生成工具解决以下问题：

两数之和
给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出和为目标值的那两个整数，并返回它们的数组下标。

示例 1：
输入：nums = [2,7,11,15], target = 9
输出：[0,1]
解释：因为 nums[0] + nums[1] == 9，所以返回 [0, 1]。
```

**控制实例数量**：
```
请使用代码生成工具生成3个快速排序算法实例
```

**输出示例**：
```
🎯 代码生成完成！

📋 问题: 两数之和...

🔄 迭代次数: 1

✅ 测试结果: 7/7 通过

💻 生成的函数:
def run_code(nums, target):
    num_to_index = {}
    for i, num in enumerate(nums):
        complement = target - num
        if complement in num_to_index:
            return [num_to_index[complement], i]
        num_to_index[num] = i
    return []

📊 测试详情:
1. ✅ 基本情况
2. ✅ 边界情况
3. ✅ 负数情况
...
```

**注意事项**：
- 生成的函数名固定为 `run_code`
- 支持多种数据类型的输入输出
- 自动处理边界情况和异常情况
- 提供详细的错误信息和调试信息

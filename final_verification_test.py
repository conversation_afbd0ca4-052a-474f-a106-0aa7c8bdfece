#!/usr/bin/env python3
"""
最终验证测试 - 确保 code_generator_tool 完全修复
"""

import asyncio
import sys
import os
import re

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from python.tools.code_generator_tool import CodeGeneratorTool
from agent import Agent
from initialize import initialize_agent


async def comprehensive_test():
    """全面测试修复效果"""
    
    print("🎯 === CodeGeneratorTool 最终验证测试 ===")
    print()
    
    # 初始化 Agent
    config = initialize_agent()
    agent = Agent(0, config)
    
    # 创建工具实例
    tool = CodeGeneratorTool(
        agent=agent,
        name="code_generator_tool",
        method="",
        args={},
        message=""
    )
    
    # 全面测试用例
    test_cases = [
        {
            "name": "单实例测试",
            "problem": "实现二分查找算法",
            "num_examples": 1,
            "expected_count": 1
        },
        {
            "name": "双实例测试", 
            "problem": "实现冒泡排序算法",
            "num_examples": 2,
            "expected_count": 2
        },
        {
            "name": "三实例测试",
            "problem": "实现选择排序算法", 
            "num_examples": 3,
            "expected_count": 3
        },
        {
            "name": "五实例测试",
            "problem": "实现插入排序算法",
            "num_examples": 5,
            "expected_count": 5
        },
        {
            "name": "边界测试（最大值）",
            "problem": "实现归并排序算法",
            "num_examples": 10,
            "expected_count": 10
        }
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"🧪 测试 {i}/{total_tests}: {test_case['name']}")
        print(f"   问题: {test_case['problem']}")
        print(f"   要求实例数: {test_case['num_examples']}")
        
        try:
            # 执行测试
            result = await tool.execute(
                problem=test_case['problem'],
                num_examples=test_case['num_examples'],
                max_iterations=2
            )
            
            if result and hasattr(result, 'message'):
                message = result.message
                
                # 检查测试通过率（从这里可以推断测试用例数量）
                test_result_match = re.search(r'测试结果: (\d+)/(\d+) 通过', message)
                if test_result_match:
                    passed = int(test_result_match.group(1))
                    total = int(test_result_match.group(2))
                    expected_count = test_case['expected_count']

                    if total == expected_count:
                        print(f"   ✅ 实例数量正确: {total}/{expected_count}")

                        # 检查是否包含代码实现
                        if "生成的函数:" in message:
                            print("   ✅ 包含代码实现")

                            if passed == total:
                                print(f"   ✅ 所有测试通过: {passed}/{total}")
                                passed_tests += 1
                            else:
                                print(f"   ⚠️ 部分测试失败: {passed}/{total}")
                        else:
                            print("   ❌ 缺少代码实现")
                    else:
                        print(f"   ❌ 实例数量不匹配: {total}/{expected_count}")
                else:
                    print("   ❌ 无法解析测试结果")
            else:
                print("   ❌ 无返回结果")
                
        except Exception as e:
            print(f"   ❌ 执行失败: {str(e)}")
        
        print()
    
    # 输出总结
    print("=" * 60)
    print(f"🎉 测试完成！通过率: {passed_tests}/{total_tests} ({100*passed_tests//total_tests}%)")
    
    if passed_tests == total_tests:
        print("✅ 所有测试通过！CodeGeneratorTool 修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False


async def test_edge_cases():
    """测试边界情况"""
    
    print()
    print("🔍 === 边界情况测试 ===")
    print()
    
    # 初始化 Agent
    config = initialize_agent()
    agent = Agent(0, config)
    
    # 创建工具实例
    tool = CodeGeneratorTool(
        agent=agent,
        name="code_generator_tool",
        method="",
        args={},
        message=""
    )
    
    edge_cases = [
        {
            "name": "空问题测试",
            "kwargs": {"problem": "", "num_examples": 1},
            "should_fail": True
        },
        {
            "name": "超出范围测试（过大）",
            "kwargs": {"problem": "排序算法", "num_examples": 15},
            "should_fail": False,  # 应该被限制到10
            "expected_max": 10
        },
        {
            "name": "超出范围测试（过小）",
            "kwargs": {"problem": "排序算法", "num_examples": 0},
            "should_fail": False,  # 应该被限制到1
            "expected_min": 1
        }
    ]
    
    for edge_case in edge_cases:
        print(f"🧪 {edge_case['name']}")
        
        try:
            result = await tool.execute(**edge_case['kwargs'])
            
            if edge_case['should_fail']:
                if result and "请提供编程问题描述" in result.message:
                    print("   ✅ 正确处理空问题")
                else:
                    print("   ⚠️ 应该失败但没有失败")
            else:
                if result and hasattr(result, 'message'):
                    message = result.message
                    test_result_match = re.search(r'测试结果: (\d+)/(\d+) 通过', message)
                    if test_result_match:
                        passed = int(test_result_match.group(1))
                        total = int(test_result_match.group(2))

                        if 'expected_max' in edge_case and total <= edge_case['expected_max']:
                            print(f"   ✅ 正确限制最大值: {total} <= {edge_case['expected_max']}")
                        elif 'expected_min' in edge_case and total >= edge_case['expected_min']:
                            print(f"   ✅ 正确限制最小值: {total} >= {edge_case['expected_min']}")
                        else:
                            print(f"   ✅ 生成了 {total} 个测试用例")
                    else:
                        print("   ⚠️ 无法解析结果")
                else:
                    print("   ❌ 无返回结果")
                    
        except Exception as e:
            print(f"   ❌ 执行失败: {str(e)}")
        
        print()


if __name__ == "__main__":
    print("🚀 开始最终验证测试...")
    print()
    
    # 运行全面测试
    success = asyncio.run(comprehensive_test())
    
    # 运行边界情况测试
    asyncio.run(test_edge_cases())
    
    print()
    if success:
        print("🎉 最终验证通过！CodeGeneratorTool 修复完成！")
    else:
        print("⚠️ 最终验证未完全通过，请检查问题")

#!/bin/bash

# PocketFlow 工具创建脚本
# 用法: ./scripts/create_new_tool.sh <tool_name> [description]

set -e

# 检查参数
if [ $# -lt 1 ]; then
    echo "用法: $0 <tool_name> [description]"
    echo "示例: $0 data_analyzer '数据分析工具'"
    exit 1
fi

TOOL_NAME=$1
DESCRIPTION=${2:-"基于 PocketFlow 的自定义工具"}

# 转换工具名称格式
TOOL_CLASS_NAME=$(echo $TOOL_NAME | sed 's/_\([a-z]\)/\U\1/g' | sed 's/^./\U&/')Tool
TOOL_FILE_NAME="${TOOL_NAME}.py"
PROMPT_FILE_NAME="agent.system.tool.${TOOL_NAME}.md"

echo "🚀 创建新的 PocketFlow 工具: $TOOL_NAME"
echo "📝 类名: $TOOL_CLASS_NAME"
echo "📄 描述: $DESCRIPTION"

# 检查文件是否已存在
if [ -f "python/tools/$TOOL_FILE_NAME" ]; then
    echo "❌ 错误: 工具文件已存在: python/tools/$TOOL_FILE_NAME"
    exit 1
fi

if [ -f "prompts/default/$PROMPT_FILE_NAME" ]; then
    echo "❌ 错误: 提示文件已存在: prompts/default/$PROMPT_FILE_NAME"
    exit 1
fi

# 创建工具文件
echo "📁 创建工具文件: python/tools/$TOOL_FILE_NAME"
cat > "python/tools/$TOOL_FILE_NAME" << EOF
from python.helpers.tool import Tool, Response
from python.helpers.pocketflow_adapter import AgentZeroLLMAdapter, YAMLParser
from pocketflow import Node, Flow, BatchNode
import asyncio


class ${TOOL_CLASS_NAME}Node(Node):
    """${DESCRIPTION}的核心处理节点"""
    
    async def run_async(self, shared):
        """执行核心处理逻辑"""
        
        input_data = shared.get("input_data", "")
        llm_adapter = shared.get("llm_adapter")
        
        print(f"🔄 处理数据: {str(input_data)[:50]}...")
        
        try:
            # TODO: 实现您的具体处理逻辑
            prompt = f"""
            请处理以下数据：
            {input_data}
            
            处理要求：
            1. 分析数据内容
            2. 提取关键信息
            3. 生成处理结果
            
            请以YAML格式返回结果：
            \`\`\`yaml
            analysis: |
              数据分析结果...
            
            key_points:
              - 关键点1
              - 关键点2
            
            result: |
              处理后的结果...
            \`\`\`
            """
            
            response = await llm_adapter.call_llm(prompt)
            
            # 解析 YAML 响应
            yaml_str = YAMLParser.extract_yaml_from_response(response)
            result = YAMLParser.safe_load(yaml_str)
            
            shared["processing_result"] = result
            
            print(f"✅ 处理完成")
            
        except Exception as e:
            shared["processing_result"] = {"error": str(e)}
            print(f"❌ 处理失败: {e}")


class $TOOL_CLASS_NAME(Tool):
    """
    $DESCRIPTION
    
    功能特性：
    - 智能数据处理
    - 结构化输出
    - 错误处理和重试
    """
    
    async def execute(self, input_data="", **kwargs):
        """
        执行工具逻辑
        
        Args:
            input_data: 输入数据
            **kwargs: 其他参数
        
        Returns:
            Response: 处理结果
        """
        
        if not input_data:
            return Response(
                message="❌ 请提供输入数据",
                break_loop=False
            )
        
        try:
            # 创建适配器
            llm_adapter = AgentZeroLLMAdapter(self.agent)
            
            # 初始化共享状态
            shared = {
                "input_data": input_data,
                "llm_adapter": llm_adapter,
                "tool_args": kwargs,
                "start_time": asyncio.get_event_loop().time()
            }
            
            print(f"🚀 开始执行 $TOOL_NAME")
            print(f"📝 输入数据: {str(input_data)[:100]}...")
            
            # 运行工作流
            await self._run_workflow(shared)
            
            # 生成报告
            result = self._generate_report(shared)
            
            return Response(
                message=result,
                break_loop=False
            )
            
        except Exception as e:
            return Response(
                message=f"❌ 执行失败: {str(e)}",
                break_loop=False
            )
    
    async def _run_workflow(self, shared):
        """运行工作流"""
        
        # 执行处理节点
        processing_node = ${TOOL_CLASS_NAME}Node()
        await processing_node.run_async(shared)
    
    def _generate_report(self, shared):
        """生成执行报告"""
        
        result = shared.get("processing_result", {})
        execution_time = asyncio.get_event_loop().time() - shared["start_time"]
        
        if "error" in result:
            return f"❌ 处理失败: {result['error']}"
        
        report = f"""
🎯 $DESCRIPTION 执行报告

⏱️ 执行时间: {execution_time:.2f} 秒

🔍 分析结果:
{result.get('analysis', '无分析结果')}

📋 关键要点:
"""
        
        for point in result.get('key_points', []):
            report += f"• {point}\n"
        
        report += f"""
📄 处理结果:
{result.get('result', '无处理结果')}

✅ 执行状态: 完成
"""
        
        return report
EOF

# 创建系统提示文件
echo "📝 创建系统提示文件: prompts/default/$PROMPT_FILE_NAME"
cat > "prompts/default/$PROMPT_FILE_NAME" << EOF
## ${TOOL_NAME^} Tool

$DESCRIPTION，基于 PocketFlow 框架构建。

**功能特性**：
- 🔄 **智能处理**：自动分析和处理输入数据
- 🧠 **深度分析**：提取关键信息和洞察
- 📊 **结构化输出**：生成格式化的处理结果
- ⚡ **高效执行**：优化的工作流程

**使用方法**：
\`\`\`json
{
  "tool_name": "$TOOL_NAME",
  "parameters": {
    "input_data": "需要处理的数据内容"
  }
}
\`\`\`

**参数说明**：
- \`input_data\` (必需): 需要处理的原始数据，支持文本、JSON等格式

**适用场景**：
- 📋 **数据分析**：分析和理解复杂数据
- 🔍 **信息提取**：从数据中提取关键信息
- ✍️ **内容处理**：处理和转换文本内容
- 🤖 **智能决策**：基于数据做出智能判断

**示例用法**：
\`\`\`
请使用 $TOOL_NAME 处理以下数据：
[您的数据内容]
\`\`\`

**输出示例**：
\`\`\`
🎯 $DESCRIPTION 执行报告

⏱️ 执行时间: 2.3 秒

🔍 分析结果:
[详细的分析结果]

📋 关键要点:
• [关键点1]
• [关键点2]

📄 处理结果:
[处理后的结果]

✅ 执行状态: 完成
\`\`\`

**注意事项**：
- 支持多种数据格式输入
- 自动处理错误和异常情况
- 提供详细的执行日志和报告
EOF

# 注册工具到系统
echo "🔧 注册工具到系统"
if ! grep -q "$PROMPT_FILE_NAME" prompts/default/agent.system.tools.md; then
    echo "" >> prompts/default/agent.system.tools.md
    echo "{{ include './$PROMPT_FILE_NAME' }}" >> prompts/default/agent.system.tools.md
    echo "✅ 已添加到工具列表"
else
    echo "⚠️ 工具已在列表中"
fi

# 创建测试文件
echo "🧪 创建测试文件: examples/test_${TOOL_NAME}.py"
cat > "examples/test_${TOOL_NAME}.py" << EOF
#!/usr/bin/env python3
"""
$TOOL_NAME 工具测试
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from initialize import initialize_agent
from agent import Agent
from python.tools.$TOOL_FILE_NAME import $TOOL_CLASS_NAME


async def test_${TOOL_NAME}():
    """测试 $TOOL_NAME 工具"""
    
    print(f"🚀 开始测试 $TOOL_NAME 工具...")
    
    # 使用项目默认配置
    config = initialize_agent()
    agent = Agent(0, config)
    
    # 创建工具实例
    tool = $TOOL_CLASS_NAME(
        agent=agent,
        name="$TOOL_NAME",
        method="",
        args={
            "input_data": "这是一个测试数据，用于验证工具的基本功能。"
        },
        message=""
    )
    
    print("📝 测试数据：测试文本")
    print("⏳ 开始处理...")
    
    try:
        # 执行工具
        result = await tool.execute(
            input_data="这是一个测试数据，用于验证工具的基本功能。包含多种信息类型，需要进行分析和处理。"
        )
        
        print("\\n" + "="*60)
        print("🎉 测试完成！")
        print("="*60)
        print(result.message)
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print(f"🎯 $TOOL_NAME 工具测试")
    print("="*60)
    
    # 运行异步测试
    try:
        asyncio.run(test_${TOOL_NAME}())
    except KeyboardInterrupt:
        print("\\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\\n❌ 测试过程中出现错误: {e}")


if __name__ == "__main__":
    main()
EOF

# 设置执行权限
chmod +x "examples/test_${TOOL_NAME}.py"

echo ""
echo "🎉 工具创建完成！"
echo ""
echo "📁 创建的文件："
echo "  • python/tools/$TOOL_FILE_NAME"
echo "  • prompts/default/$PROMPT_FILE_NAME"
echo "  • examples/test_${TOOL_NAME}.py"
echo ""
echo "🚀 下一步："
echo "  1. 编辑 python/tools/$TOOL_FILE_NAME 实现具体逻辑"
echo "  2. 运行测试: python examples/test_${TOOL_NAME}.py"
echo "  3. 在 Agent Zero 中测试: '请使用 $TOOL_NAME 处理数据'"
echo ""
echo "📚 参考文档: POCKETFLOW_EXTENSION_GUIDE.md"

#!/bin/bash

# API密钥检查脚本
# 检查和配置Agent-Zero项目所需的API密钥

echo "🔑 === API密钥配置检查 ==="
echo ""

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV_FILE="$SCRIPT_DIR/.env"
EXAMPLE_ENV_FILE="$SCRIPT_DIR/example.env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -c, --create        创建.env文件（如果不存在）"
    echo "  -i, --interactive   交互式配置API密钥"
    echo "  -v, --verbose       详细输出模式"
    echo "  -t, --test          测试API密钥连接"
    echo ""
    echo "功能:"
    echo "  - 检查.env文件是否存在"
    echo "  - 验证API密钥配置"
    echo "  - 显示缺失的API密钥"
    echo "  - 支持交互式配置"
    echo ""
}

# 解析命令行参数
CREATE_ENV=false
INTERACTIVE=false
VERBOSE=false
TEST_KEYS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--create)
            CREATE_ENV=true
            shift
            ;;
        -i|--interactive)
            INTERACTIVE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -t|--test)
            TEST_KEYS=true
            shift
            ;;
        *)
            echo "❌ 未知选项: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 详细输出函数
verbose_echo() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}🔍 [详细] $1${NC}"
    fi
}

# API密钥列表
declare -A API_KEYS=(
    ["OPENAI"]="OpenAI API Key"
    ["ANTHROPIC"]="Anthropic API Key"
    ["GROQ"]="Groq API Key"
    ["GOOGLE"]="Google API Key"
    ["MISTRALAI"]="MistralAI API Key"
    ["DEEPSEEK"]="DeepSeek API Key"
    ["SILICONFLOW"]="SiliconFlow API Key"
    ["VOLCENGINE"]="VolcEngine API Key"
    ["OPENROUTER"]="OpenRouter API Key"
    ["SAMBANOVA"]="Sambanova API Key"
    ["HUGGINGFACE"]="HuggingFace Token"
    ["CHUTES"]="Chutes API Key"
)

# 检查.env文件
check_env_file() {
    echo "📁 检查.env文件..."
    
    if [ ! -f "$ENV_FILE" ]; then
        echo -e "${YELLOW}⚠️  .env文件不存在${NC}"
        
        if [ "$CREATE_ENV" = true ]; then
            echo "🔧 创建.env文件..."
            if [ -f "$EXAMPLE_ENV_FILE" ]; then
                cp "$EXAMPLE_ENV_FILE" "$ENV_FILE"
                echo -e "${GREEN}✅ 已从example.env创建.env文件${NC}"
            else
                touch "$ENV_FILE"
                echo -e "${GREEN}✅ 已创建空的.env文件${NC}"
            fi
        else
            echo "💡 提示: 使用 -c 选项自动创建.env文件"
            return 1
        fi
    else
        echo -e "${GREEN}✅ .env文件存在${NC}"
        verbose_echo ".env文件路径: $ENV_FILE"
    fi
    
    return 0
}

# 检查API密钥
check_api_keys() {
    echo ""
    echo "🔑 检查API密钥配置..."
    
    # 加载.env文件
    if [ -f "$ENV_FILE" ]; then
        source "$ENV_FILE"
        verbose_echo "已加载.env文件"
    fi
    
    local missing_keys=()
    local configured_keys=()
    
    for key in "${!API_KEYS[@]}"; do
        local env_var="API_KEY_$key"
        local value="${!env_var}"
        
        if [ -n "$value" ] && [ "$value" != "" ]; then
            configured_keys+=("$key")
            echo -e "${GREEN}✅ $key: 已配置${NC}"
            verbose_echo "$env_var=${value:0:8}..."
        else
            missing_keys+=("$key")
            echo -e "${RED}❌ $key: 未配置${NC}"
        fi
    done
    
    echo ""
    echo "📊 配置统计:"
    echo -e "   ${GREEN}已配置: ${#configured_keys[@]}${NC}"
    echo -e "   ${RED}未配置: ${#missing_keys[@]}${NC}"
    echo -e "   ${BLUE}总计: ${#API_KEYS[@]}${NC}"
    
    if [ ${#missing_keys[@]} -gt 0 ]; then
        echo ""
        echo -e "${YELLOW}⚠️  缺失的API密钥:${NC}"
        for key in "${missing_keys[@]}"; do
            echo "   - API_KEY_$key (${API_KEYS[$key]})"
        done
        
        if [ "$INTERACTIVE" = true ]; then
            configure_missing_keys "${missing_keys[@]}"
        fi
        
        return 1
    else
        echo -e "${GREEN}🎉 所有API密钥都已配置！${NC}"
        return 0
    fi
}

# 交互式配置缺失的API密钥
configure_missing_keys() {
    local missing_keys=("$@")
    
    echo ""
    echo "🔧 交互式配置API密钥..."
    echo "💡 提示: 直接按回车跳过某个密钥的配置"
    echo ""
    
    for key in "${missing_keys[@]}"; do
        local env_var="API_KEY_$key"
        local description="${API_KEYS[$key]}"
        
        echo -n "请输入 $description ($env_var): "
        read -r api_key
        
        if [ -n "$api_key" ]; then
            # 添加到.env文件
            if grep -q "^$env_var=" "$ENV_FILE" 2>/dev/null; then
                # 更新现有行
                sed -i "s/^$env_var=.*/$env_var=$api_key/" "$ENV_FILE"
            else
                # 添加新行
                echo "$env_var=$api_key" >> "$ENV_FILE"
            fi
            echo -e "${GREEN}✅ $key 已配置${NC}"
        else
            echo -e "${YELLOW}⏭️  跳过 $key${NC}"
        fi
        echo ""
    done
    
    echo -e "${GREEN}✅ 交互式配置完成${NC}"
}

# 测试API密钥连接
test_api_keys() {
    echo ""
    echo "🧪 测试API密钥连接..."
    
    # 这里可以添加实际的API连接测试
    # 目前只是示例
    echo -e "${YELLOW}⚠️  API连接测试功能开发中...${NC}"
    echo "💡 您可以使用以下脚本进行详细测试:"
    echo "   - python test_api_keys.py"
    echo "   - python test_new_providers.py"
}

# 显示配置建议
show_recommendations() {
    echo ""
    echo "💡 配置建议:"
    echo ""
    echo "🔥 推荐的API密钥（按优先级）:"
    echo "   1. OpenAI - 最成熟的AI服务"
    echo "   2. Anthropic - Claude模型，优秀的对话能力"
    echo "   3. SiliconFlow - 国内服务，访问速度快"
    echo "   4. DeepSeek - 国产AI，性价比高"
    echo "   5. Groq - 推理速度极快"
    echo ""
    echo "🌐 国内用户推荐:"
    echo "   - SiliconFlow: https://siliconflow.cn/"
    echo "   - VolcEngine: https://www.volcengine.com/"
    echo "   - DeepSeek: https://www.deepseek.com/"
    echo ""
    echo "🔗 获取API密钥链接:"
    echo "   - OpenAI: https://platform.openai.com/api-keys"
    echo "   - Anthropic: https://console.anthropic.com/"
    echo "   - Groq: https://console.groq.com/keys"
    echo "   - Google: https://makersuite.google.com/app/apikey"
    echo ""
}

# 主函数
main() {
    # 检查.env文件
    if ! check_env_file; then
        if [ "$CREATE_ENV" = false ]; then
            echo ""
            echo -e "${RED}❌ 无法继续，请先创建.env文件${NC}"
            echo "💡 使用: $0 --create"
            exit 1
        fi
    fi
    
    # 检查API密钥
    local keys_ok=true
    if ! check_api_keys; then
        keys_ok=false
    fi
    
    # 测试API密钥（如果请求）
    if [ "$TEST_KEYS" = true ]; then
        test_api_keys
    fi
    
    # 显示建议
    if [ "$keys_ok" = false ] || [ "$VERBOSE" = true ]; then
        show_recommendations
    fi
    
    echo ""
    if [ "$keys_ok" = true ]; then
        echo -e "${GREEN}🎉 API密钥配置检查完成，一切正常！${NC}"
        exit 0
    else
        echo -e "${YELLOW}⚠️  API密钥配置不完整，但可以继续运行${NC}"
        echo "💡 使用 $0 --interactive 进行交互式配置"
        exit 0  # 不完整但不阻止启动
    fi
}

# 运行主函数
main "$@"

#!/bin/bash

# 安装系统级ffmpeg脚本
# 用于Agent-Zero项目的原生ffmpeg支持

echo "🎬 === 安装系统级FFMPEG ==="
echo ""

# 检查是否已安装
if command -v ffmpeg &> /dev/null; then
    echo "✅ FFMPEG已安装"
    ffmpeg_version=$(ffmpeg -version 2>&1 | head -n 1)
    echo "当前版本: $ffmpeg_version"
    echo ""
    read -p "是否要重新安装？(y/N): " reinstall
    if [[ ! $reinstall =~ ^[Yy]$ ]]; then
        echo "跳过安装"
        exit 0
    fi
fi

# 更新包列表
echo "📦 更新包列表..."
sudo apt update

# 安装ffmpeg
echo "🎬 安装FFMPEG..."
sudo apt install -y ffmpeg

# 验证安装
echo ""
echo "🧪 验证安装..."
if command -v ffmpeg &> /dev/null; then
    ffmpeg_version=$(ffmpeg -version 2>&1 | head -n 1)
    echo "✅ FFMPEG安装成功: $ffmpeg_version"
    
    # 测试基本功能
    echo "🔄 测试FFMPEG功能..."
    if ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 -f null - 2>/dev/null; then
        echo "✅ FFMPEG功能测试通过"
    else
        echo "⚠️  FFMPEG功能测试失败，但基本安装成功"
    fi
else
    echo "❌ FFMPEG安装失败"
    exit 1
fi

echo ""
echo "🎉 === FFMPEG安装完成！==="
echo ""
echo "📝 现在可以："
echo "   1. 重新安装项目依赖（移除imageio-ffmpeg）"
echo "   2. 使用原生ffmpeg运行Agent-Zero"
echo ""

#!/bin/bash

# WSL Python 3.12 升级和venv环境设置脚本
# 用于Agent-Zero项目

echo "🐍 === WSL Python 3.12 升级和环境设置 ==="
echo ""

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 第一步：检查当前Python版本
echo "🔍 第一步：检查当前Python版本..."
echo "当前Python3版本: $(python3 --version 2>/dev/null || echo '未安装')"
echo "当前Python版本: $(python --version 2>/dev/null || echo '未安装')"

# 第二步：更新系统并安装依赖
echo ""
echo "📦 第二步：更新系统并安装必要依赖..."
sudo apt update
sudo apt install -y software-properties-common curl build-essential

# 第三步：添加Python官方PPA
echo ""
echo "🔧 第三步：添加Python官方PPA..."
sudo add-apt-repository ppa:deadsnakes/ppa -y
sudo apt update

# 第四步：安装Python 3.12
echo ""
echo "🐍 第四步：安装Python 3.12..."
sudo apt install -y python3.12 python3.12-venv python3.12-dev python3.12-distutils

# 第五步：安装pip for Python 3.12
echo ""
echo "📦 第五步：安装pip for Python 3.12..."
curl -sS https://bootstrap.pypa.io/get-pip.py | python3.12

# 第六步：设置Python 3.12为默认版本
echo ""
echo "⚙️  第六步：设置Python 3.12为默认版本..."
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.12 1

# 如果存在其他Python版本，添加到alternatives
if command -v python3.10 &> /dev/null; then
    sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 2
fi

if command -v python3.11 &> /dev/null; then
    sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 3
fi

# 自动选择Python 3.12
echo "1" | sudo update-alternatives --config python3 2>/dev/null || true

# 创建python命令软链接
sudo ln -sf /usr/bin/python3.12 /usr/bin/python

# 第七步：验证Python安装
echo ""
echo "✅ 第七步：验证Python安装..."
echo "Python3版本: $(python3 --version)"
echo "Python版本: $(python --version)"
echo "Pip3版本: $(pip3 --version)"

# 第八步：重新创建venv环境
echo ""
echo "🔄 第八步：重新创建venv环境..."
cd "$SCRIPT_DIR"

# 删除旧的venv
if [ -d "venv" ]; then
    echo "删除旧的venv环境..."
    rm -rf venv
fi

# 创建新的venv
echo "使用Python 3.12创建新的venv环境..."
python3.12 -m venv venv

# 激活环境
source venv/bin/activate

# 验证venv中的Python版本
echo "venv中的Python版本: $(python --version)"

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 第九步：安装项目依赖
echo ""
echo "📦 第九步：安装项目依赖..."
pip install -r requirements.txt

# 第十步：安装Playwright浏览器
echo ""
echo "🎭 第十步：安装Playwright浏览器..."
playwright install

# 第十一步：验证安装
echo ""
echo "🧪 第十一步：验证安装..."
python -c "
import sys
print(f'✅ Python版本: {sys.version}')

try:
    import whisper
    print('✅ Whisper导入成功')
    
    # 测试加载小模型
    model = whisper.load_model('tiny')
    print('✅ Whisper模型加载成功')
except Exception as e:
    print(f'❌ Whisper测试失败: {e}')

try:
    import flask, openai
    print('✅ Flask和OpenAI导入成功')
except Exception as e:
    print(f'❌ 核心依赖测试失败: {e}')

try:
    import playwright
    print('✅ Playwright导入成功')
except Exception as e:
    print(f'❌ Playwright测试失败: {e}')
"

echo ""
echo "🎉 === 安装完成！==="
echo ""
echo "📝 使用说明："
echo "   激活环境: source venv/bin/activate"
echo "   启动项目: ./start_all.sh"
echo "   停用环境: deactivate"
echo ""
echo "🔍 Python版本信息："
echo "   系统Python3: $(python3 --version)"
echo "   venv Python: $(source venv/bin/activate && python --version)"
echo ""

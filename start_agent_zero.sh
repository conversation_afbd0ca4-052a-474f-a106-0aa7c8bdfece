#!/bin/bash

# Agent-Zero 独立启动脚本
# 用于在WSL环境中单独启动Agent-Zero服务，支持conda和venv虚拟环境

echo "🤖 === Agent-Zero 独立启动脚本 ==="
echo ""

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 配置变量
CONDA_ENV_NAME="A0"
VENV_PATH="$SCRIPT_DIR/venv"
LOG_FILE="/tmp/agent_zero.log"
DEFAULT_PORT="50001"
DEFAULT_HOST="0.0.0.0"

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在关闭Agent-Zero服务..."
    
    # 停止Agent-Zero进程
    pkill -f "python run_ui.py" 2>/dev/null && echo "✅ Agent-Zero已停止"
    
    # 清理后台任务
    jobs -p | xargs -r kill 2>/dev/null
    
    echo "👋 Agent-Zero服务已停止，再见！"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -p, --port PORT         指定端口号 (默认: $DEFAULT_PORT)"
    echo "  -H, --host HOST         指定主机地址 (默认: $DEFAULT_HOST)"
    echo "  -b, --background        后台运行模式"
    echo "  -e, --env ENV_TYPE      指定环境类型 (conda|venv|auto，默认: auto)"
    echo "  -c, --check-only        仅检查环境，不启动服务"
    echo "  -v, --verbose           详细输出模式"
    echo ""
    echo "示例:"
    echo "  $0                      # 使用默认设置启动"
    echo "  $0 -p 8080 -H 0.0.0.0   # 指定端口和主机"
    echo "  $0 -b                   # 后台运行"
    echo "  $0 -e conda             # 强制使用conda环境"
    echo "  $0 -c                   # 仅检查环境"
    echo ""
}

# 解析命令行参数
BACKGROUND_MODE=false
ENV_TYPE="auto"
CHECK_ONLY=false
VERBOSE=false
PORT="$DEFAULT_PORT"
HOST="$DEFAULT_HOST"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -H|--host)
            HOST="$2"
            shift 2
            ;;
        -b|--background)
            BACKGROUND_MODE=true
            shift
            ;;
        -e|--env)
            ENV_TYPE="$2"
            shift 2
            ;;
        -c|--check-only)
            CHECK_ONLY=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            echo "❌ 未知选项: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 详细输出函数
verbose_echo() {
    if [ "$VERBOSE" = true ]; then
        echo "🔍 [详细] $1"
    fi
}

# 第一步：环境检查
echo "🔍 第一步：检查环境..."

# 检查并修复API密钥配置
if [ -f "check_api_keys.sh" ]; then
    echo "🔧 检查API密钥配置..."
    bash check_api_keys.sh
    if [ $? -ne 0 ]; then
        echo "❌ API密钥配置检查失败"
        exit 1
    fi
else
    echo "⚠️  未找到API密钥检查脚本，跳过检查"
fi

# 检查conda（如果需要）
if [ "$ENV_TYPE" = "conda" ] || [ "$ENV_TYPE" = "auto" ]; then
    # 先加载bashrc以确保conda可用
    source ~/.bashrc 2>/dev/null || true
    if command -v conda &> /dev/null; then
        verbose_echo "找到conda命令"
        CONDA_AVAILABLE=true
    else
        verbose_echo "未找到conda命令"
        CONDA_AVAILABLE=false
    fi
else
    CONDA_AVAILABLE=false
fi

# 确定使用的环境类型
USE_CONDA=false
USE_VENV=false

if [ "$ENV_TYPE" = "conda" ]; then
    if [ "$CONDA_AVAILABLE" = false ]; then
        echo "❌ 错误：指定使用conda但未找到conda命令"
        exit 1
    fi
    if ! conda env list 2>/dev/null | grep -q "^$CONDA_ENV_NAME "; then
        echo "❌ 错误：未找到 $CONDA_ENV_NAME conda环境"
        echo "请先创建：conda create -n $CONDA_ENV_NAME python=3.12 -y && conda activate $CONDA_ENV_NAME && pip install -r requirements.txt"
        exit 1
    fi
    USE_CONDA=true
    verbose_echo "使用conda环境: $CONDA_ENV_NAME"
elif [ "$ENV_TYPE" = "venv" ]; then
    if [ ! -d "$VENV_PATH" ]; then
        echo "❌ 错误：指定使用venv但未找到虚拟环境目录: $VENV_PATH"
        echo "请先创建：python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
        exit 1
    fi
    USE_VENV=true
    verbose_echo "使用venv环境: $VENV_PATH"
else
    # auto模式：自动检测
    if [ "$CONDA_AVAILABLE" = true ] && conda env list 2>/dev/null | grep -q "^$CONDA_ENV_NAME "; then
        USE_CONDA=true
        verbose_echo "自动检测：使用conda环境: $CONDA_ENV_NAME"
    elif [ -d "$VENV_PATH" ]; then
        USE_VENV=true
        verbose_echo "自动检测：使用venv环境: $VENV_PATH"
    else
        echo "❌ 错误：未找到可用的虚拟环境"
        echo "请选择以下方式之一创建环境："
        echo "  Conda: conda create -n $CONDA_ENV_NAME python=3.12 -y && conda activate $CONDA_ENV_NAME && pip install -r requirements.txt"
        echo "  Venv: python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
        exit 1
    fi
fi

echo "✅ 环境检查完成"

# 第二步：检查FFMPEG环境
echo ""
echo "🔧 第二步：检查FFMPEG环境..."

# 检查系统ffmpeg
if ! command -v ffmpeg &> /dev/null; then
    echo "❌ 错误：未找到系统ffmpeg"
    echo "请安装：sudo apt update && sudo apt install ffmpeg"
    exit 1
fi

# 测试ffmpeg版本
echo "🔄 检查FFMPEG版本..."
ffmpeg_version=$(ffmpeg -version 2>&1 | head -n 1)
echo "✅ 找到FFMPEG: $ffmpeg_version"

# 第三步：验证Agent-Zero模块
echo ""
echo "🧪 第三步：验证Agent-Zero模块..."

# 激活环境进行验证
cd "$SCRIPT_DIR"
if [ "$USE_CONDA" = true ]; then
    source ~/.bashrc
    conda activate "$CONDA_ENV_NAME"
    verbose_echo "已激活conda环境: $CONDA_ENV_NAME"
elif [ "$USE_VENV" = true ]; then
    source "$VENV_PATH/bin/activate"
    verbose_echo "已激活venv环境: $VENV_PATH"
fi

# 验证模块导入
python -c "
import sys
sys.path.insert(0, '.')
try:
    from python.helpers import runtime, searxng
    print('✅ 模块导入成功')
    
    # 验证SearXNG配置
    print(f'ℹ️  SearXNG配置URL: {searxng.URL}')
    if '8888' in searxng.URL:
        print(f'✅ SearXNG端口配置正确: {searxng.URL}')
    else:
        print(f'⚠️  SearXNG端口配置: {searxng.URL}')
        print('ℹ️  注意：Agent-Zero期望SearXNG运行在端口8888')
    
    # 验证开发模式
    if runtime.is_development():
        print('✅ 开发模式已启用')
    else:
        print('ℹ️  生产模式运行')
        
except Exception as e:
    print(f'❌ 模块验证失败: {e}')
    sys.exit(1)
"

# 检查验证是否成功
if [ $? -ne 0 ]; then
    echo "❌ Agent-Zero模块验证失败，退出..."
    exit 1
fi

# 如果只是检查模式，到此结束
if [ "$CHECK_ONLY" = true ]; then
    echo ""
    echo "✅ 环境检查完成，所有组件正常"
    echo "🌐 准备启动地址: http://$HOST:$PORT"
    exit 0
fi

echo ""
echo "🌐 服务信息："
echo "   📱 Agent-Zero: http://$HOST:$PORT"
echo "   🔍 SearXNG: 需要单独启动 (./start_searxng.sh)"
echo ""

# 第四步：启动Agent-Zero
echo "🚀 第四步：启动Agent-Zero..."

if [ "$BACKGROUND_MODE" = true ]; then
    echo "🔄 后台模式启动..."
    
    # 后台启动Agent-Zero
    nohup bash -c "
        cd '$SCRIPT_DIR'
        if [ '$USE_CONDA' = true ]; then
            source ~/.bashrc
            conda activate '$CONDA_ENV_NAME'
        elif [ '$USE_VENV' = true ]; then
            source '$VENV_PATH/bin/activate'
        fi
        python run_ui.py --port=$PORT --host=$HOST
    " > "$LOG_FILE" 2>&1 &
    
    AGENT_PID=$!
    echo "✅ Agent-Zero已在后台启动，PID: $AGENT_PID"
    
    # 等待Agent-Zero启动
    echo "⏳ 等待Agent-Zero初始化..."
    for i in {1..30}; do
        if curl -s "http://$HOST:$PORT" > /dev/null 2>&1; then
            echo "✅ Agent-Zero运行正常：http://$HOST:$PORT"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ Agent-Zero启动超时"
            echo "📝 检查日志：tail -f $LOG_FILE"
            exit 1
        fi
        sleep 1
    done
    
    echo ""
    echo "🌐 Agent-Zero服务信息："
    echo "   📱 访问地址: http://$HOST:$PORT"
    echo "   📝 日志文件: $LOG_FILE"
    echo "   🔧 进程ID: $AGENT_PID"
    echo ""
    echo "📝 查看日志: tail -f $LOG_FILE"
    echo "📝 停止服务: pkill -f 'python run_ui.py' 或者 kill $AGENT_PID"
    echo ""
    
else
    echo "🔄 前台模式启动..."
    echo "📝 按 Ctrl+C 停止服务"
    echo ""
    
    # 前台启动Agent-Zero
    python run_ui.py --port="$PORT" --host="$HOST"
    
    # 如果到达这里，说明Agent-Zero被停止了
    cleanup
fi

"""
New Tool Recommendations - 新工具温和推荐扩展
在保持原生工具选择的基础上，为特定场景温和推荐新增工具
"""
from python.helpers.extension import Extension
from python.helpers.tool_selector import tool_selector
from agent import Agent, LoopData
from typing import Any


class NewToolRecommendations(Extension):
    """新工具推荐扩展类"""

    async def execute(self, system_prompt: list[str] = [], loop_data: LoopData = LoopData(), **kwargs: Any):
        """执行扩展，添加工具推荐到系统提示"""

        # 获取用户输入
        user_input = self._get_user_input(loop_data)

        if user_input:
            # 生成推荐内容
            recommendation = self._generate_tool_recommendations(user_input)

            if recommendation:
                system_prompt.append(recommendation)

    def _get_user_input(self, loop_data: LoopData) -> str:
        """从 loop_data 中获取用户输入"""
        # 方法1：从 loop_data.user_message 获取
        if hasattr(loop_data, 'user_message') and loop_data.user_message:
            message = loop_data.user_message
            # 处理 history.Message 对象
            if hasattr(message, 'content'):
                content = message.content
                # 如果content是字典，提取message字段
                if isinstance(content, dict) and 'message' in content:
                    return str(content['message'])
                # 如果content是字符串，直接返回
                elif isinstance(content, str):
                    return content
                else:
                    return str(content)
            else:
                return str(message)

        # 方法2：从 agent.last_user_message 获取
        if hasattr(self.agent, 'last_user_message') and self.agent.last_user_message:
            message = self.agent.last_user_message
            if hasattr(message, 'content'):
                content = message.content
                if isinstance(content, dict) and 'message' in content:
                    return str(content['message'])
                elif isinstance(content, str):
                    return content
                else:
                    return str(content)

        # 方法3：从 agent.history 获取最后一条用户消息
        if hasattr(self.agent, 'history') and hasattr(self.agent.history, 'messages'):
            messages = self.agent.history.messages
            if messages:
                # 获取最后一条用户消息（ai=False）
                for message in reversed(messages):
                    if hasattr(message, 'ai') and not message.ai:
                        if hasattr(message, 'content'):
                            content = message.content
                            if isinstance(content, dict) and 'message' in content:
                                return str(content['message'])
                            elif isinstance(content, str):
                                return content
                            else:
                                return str(content)

        return ""

    def _generate_tool_recommendations(self, user_input: str) -> str:
        """生成工具推荐内容"""

        if not user_input:
            return ""

        # 分析用户输入
        analysis = tool_selector.analyze_user_input(user_input)

        # 生成推荐信息
        recommendations = []

        if analysis['enhanced_search_engine']['recommended']:
            confidence = analysis['enhanced_search_engine']['confidence']
            keywords = ', '.join(analysis['enhanced_search_engine']['trigger_keywords'])

            recommendations.append(f"""
## Enhanced Search Engine Tool Available
- **Trigger confidence**: {confidence:.1%}
- **Detected keywords**: {keywords}
- **Use case**: Deep research, comprehensive analysis, detailed information gathering
- **Features**: Multi-round search, quality assessment, intelligent summarization
- **When to use**: When user requests "深入", "详细", "全面", "研究" type analysis
""")

        if analysis['sequential_thinking']['recommended']:
            confidence = analysis['sequential_thinking']['confidence']
            keywords = ', '.join(analysis['sequential_thinking']['trigger_keywords'])

            recommendations.append(f"""
## Sequential Thinking Tool Available
- **Trigger confidence**: {confidence:.1%}
- **Detected keywords**: {keywords}
- **Use case**: Structured analysis, systematic problem solving, logical reasoning
- **Features**: Problem decomposition, step-by-step analysis, conclusion integration
- **When to use**: When user requests "系统", "结构", "分步", "逻辑" type analysis
""")

        if analysis['web_crawler']['recommended']:
            confidence = analysis['web_crawler']['confidence']
            keywords = ', '.join(analysis['web_crawler']['trigger_keywords'])

            recommendations.append(f"""
## Web Crawler Tool Available
- **Trigger confidence**: {confidence:.1%}
- **Detected keywords**: {keywords}
- **Use case**: Intelligent web content extraction, data collection, page scraping
- **Features**: LLM-generated crawling strategies, smart site recognition, multi-format extraction
- **When to use**: When user requests "爬取", "抓取", "采集", "提取" web content
""")

        if recommendations:
            return f"""
# 🔧 Advanced Tool Recommendations

Based on the user's request, the following specialized tools are available and recommended:

{chr(10).join(recommendations)}

## Usage Guidelines:
- These are **gentle recommendations** based on keyword analysis
- You can choose to use these tools OR continue with standard tools
- The standard tools (search_engine, browser_agent, etc.) remain fully available
- Only suggest these tools if they clearly match the user's intent
- Always explain why you're choosing a particular tool

## Tool Selection Strategy:
1. **For deep research needs**: Consider enhanced_search_engine
2. **For structured analysis needs**: Consider sequential_thinking
3. **For web content extraction**: Consider web_crawler
4. **For general tasks**: Continue using standard tools
5. **When in doubt**: Use standard tools (they work great!)

## 🚨 IMPORTANT: Enhanced Search Engine Priority
If the user explicitly mentions "enhanced search" or "enhance search", **STRONGLY CONSIDER** using the enhanced_search_engine tool. This tool is specifically designed for comprehensive research and provides advanced search capabilities.

## 🚨 IMPORTANT: Sequential Thinking Priority
If the user explicitly mentions "sequential thinking" or requests systematic analysis, **STRONGLY CONSIDER** using the sequential_thinking tool. This tool provides structured problem-solving approaches.

## 🚨 IMPORTANT: Web Crawler Priority
If the user is requesting web content extraction, data collection, or page scraping, **STRONGLY CONSIDER** using the web_crawler tool instead of browser_agent. This tool is specifically designed for intelligent content extraction workflows.

Remember: These new tools are **supplements**, not replacements. The original simple and effective tools should remain your primary choice unless there's a clear benefit to using the specialized tools.
"""

        return ""

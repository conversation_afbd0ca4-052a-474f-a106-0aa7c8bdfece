from python.helpers.extension import Extension
from agent import LoopData
from typing import Any


class EnhancedToolsGuide(Extension):
    """增强工具使用指导"""

    async def execute(self, system_prompt: list[str] = [], loop_data: LoopData = LoopData(), **kwargs: Any):
        """添加增强工具使用指导到系统提示"""
        
        guide_text = """
🔍 重要工具使用指导 - 专用工具选择

⚠️ 关键规则：根据用户关键词选择正确的专用工具：

## 1️⃣ ENHANCED SEARCH ENGINE (增强搜索)
触发关键词：
- "enhanced search" 或 "enhance search"（英文）
- "深入搜索"、"详细搜索"、"全面搜索"（中文）
- "深入"、"详细"、"全面"、"研究"、"深度"等修饰词

示例：
✅ "enhanced search 搜索原油价格趋势" → enhanced_search_engine
✅ "深入研究人工智能发展" → enhanced_search_engine
✅ "详细分析市场情况" → enhanced_search_engine

特点：多轮搜索、质量评估、智能摘要、全面信息收集

## 2️⃣ SEQUENTIAL THINKING (序列化思维)
触发关键词：
- "sequential thinking" 或 "sequential analysis"（英文）
- "系统"、"结构"、"分步"、"逻辑"、"分析"、"框架"（中文）
- "步骤"、"流程"、"方法论"、"思路"、"推理"、"论证"
- "systematic analysis"、"structured thinking"、"step by step"

示例：
✅ "sequential thinking 分析这个问题" → sequential_thinking
✅ "系统分析这个问题" → sequential_thinking
✅ "用结构化的方法分析" → sequential_thinking
✅ "systematic analysis of the issue" → sequential_thinking

特点：5步结构化分析、问题分解、逻辑推理、综合结论

## 3️⃣ WEB CRAWLER (智能网页爬虫)
触发关键词：
- "爬取"、"抓取"、"采集"、"收集"、"获取"、"提取"（中文）
- "crawl"、"scrape"、"extract"、"collect"、"gather"（英文）
- "网页内容"、"网站数据"、"页面信息"、"数据采集"

示例：
✅ "爬取这个网站的内容" → web_crawler
✅ "提取网页中的产品信息" → web_crawler
✅ "crawl this website for data" → web_crawler

特点：LLM自主生成爬取策略、智能网站识别、多格式提取

## 🎯 工具选择原则：
- 用户说"enhanced search" + 搜索需求 → enhanced_search_engine
- 用户说"sequential thinking" + 分析需求 → sequential_thinking
- 用户说"爬取"/"crawl" + 网页需求 → web_crawler
- 搜索信息 + 深入关键词 → enhanced_search_engine
- 分析问题 + 结构关键词 → sequential_thinking
- 网页内容 + 提取关键词 → web_crawler
- 普通搜索 → search_engine
- 普通分析 → 直接回答
- 普通网页访问 → browser_agent

⚠️ 特别注意：
- 当用户明确说"sequential thinking"时，必须使用sequential_thinking工具
- 当用户明确说"enhanced search"时，必须使用enhanced_search_engine工具
- 当用户明确说"爬取"/"crawl"时，优先考虑web_crawler工具

请严格按照用户的关键词选择正确的专用工具！
"""

        system_prompt.append(guide_text.strip())

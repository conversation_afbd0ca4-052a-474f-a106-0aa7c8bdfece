"""
Web Crawler Tool - 基于Crawl4AI的智能网页爬取工具
提供高性能、LLM友好的网页内容提取功能，支持LLM自主生成爬取策略
"""
import asyncio
import json
import os
import re
from typing import Dict, Any, List, Optional, Union
from python.helpers.tool import Tool, Response
from python.helpers.print_style import PrintStyle


class WebCrawler(Tool):
    """智能网页爬取工具"""

    def __init__(self, agent, name: str, method: str = None, args: dict = None, message: str = ""):
        super().__init__(agent, name, method, args or {}, message)
        self.crawler = None
        self._check_dependencies()

    def _check_dependencies(self):
        """检查Crawl4AI依赖"""
        try:
            import crawl4ai
            self.crawl4ai_available = True
            print("✅ Crawl4AI依赖检查通过")
        except ImportError:
            self.crawl4ai_available = False
            print("⚠️ Crawl4AI未安装，请运行: pip install crawl4ai")

    async def execute(self, url: str = "", user_intent: str = "", auto_strategy: bool = True, 
                     extract_type: str = "markdown", css_selector: str = "", 
                     extraction_schema: dict = None, js_code: str = "", 
                     wait_for: str = "", content_filter: str = "pruning", 
                     filter_threshold: float = 0.48, user_query: str = "", **kwargs):
        """
        执行智能网页爬取

        参数:
        - url: 目标网页URL
        - user_intent: 用户意图描述
        - auto_strategy: 是否自动生成爬取策略
        - extract_type: 提取类型 ("markdown", "text", "structured")
        - css_selector: CSS选择器
        - extraction_schema: 结构化提取模式
        - js_code: JavaScript代码
        - wait_for: 等待元素
        - content_filter: 内容过滤器
        - filter_threshold: 过滤阈值
        - user_query: 用户查询
        """
        if not url:
            return Response(message="请提供要爬取的网页URL", break_loop=False)

        if not self.crawl4ai_available:
            return Response(
                message="Crawl4AI未安装，请先安装: pip install crawl4ai", 
                break_loop=False
            )

        try:
            print(f"🕷️ 开始爬取网页: {url}")

            # 如果启用自动策略生成
            if auto_strategy and user_intent:
                print("🧠 使用项目LLM协调系统生成爬取策略...")
                strategy = await self._generate_crawl_strategy(url, user_intent)
                
                # 应用生成的策略
                extract_type = strategy.get('extract_type', extract_type)
                css_selector = strategy.get('css_selector', css_selector)
                extraction_schema = strategy.get('extraction_schema', extraction_schema)
                js_code = strategy.get('js_code', js_code)
                wait_for = strategy.get('wait_for', wait_for)
                content_filter = strategy.get('content_filter', content_filter)
                filter_threshold = strategy.get('filter_threshold', filter_threshold)

                print(f"📋 生成的策略: {strategy.get('reasoning', '智能策略已生成')}")

            # 执行爬取
            result = await self._crawl_with_strategy(
                url=url,
                extract_type=extract_type,
                css_selector=css_selector,
                extraction_schema=extraction_schema,
                js_code=js_code,
                wait_for=wait_for,
                content_filter=content_filter,
                filter_threshold=filter_threshold,
                user_query=user_query
            )

            return Response(message=result, break_loop=False)

        except Exception as e:
            error_msg = f"网页爬取失败: {str(e)}"
            print(f"❌ {error_msg}")
            return Response(message=error_msg, break_loop=False)

    async def _generate_crawl_strategy(self, url: str, user_intent: str, page_preview: str = None) -> Dict[str, Any]:
        """使用项目LLM协调系统生成智能爬取策略"""
        try:
            strategy_prompt = f"""
根据以下信息生成网页爬取策略：

URL: {url}
用户意图: {user_intent}
{f"页面预览: {page_preview[:200]}..." if page_preview else ""}

请直接返回JSON对象，包含以下字段：
- extract_type: "markdown" | "text" | "structured"
- css_selector: CSS选择器字符串
- content_filter: "pruning" | "bm25"
- filter_threshold: 0.0-1.0之间的数值
- reasoning: 策略选择理由

示例：
{{"extract_type": "markdown", "css_selector": "article, .content", "content_filter": "pruning", "filter_threshold": 0.48, "reasoning": "通用内容提取"}}

只返回JSON，不要其他文本：
"""

            print("🧠 使用项目LLM协调系统生成爬取策略...")
            response = await self.agent.call_utility_model(
                system="你是一个网页爬取专家，请根据用户需求生成最优的爬取策略。",
                message=strategy_prompt
            )

            # 解析JSON响应
            try:
                # 调试：打印LLM原始响应
                print(f"🔍 LLM原始响应长度: {len(response)}")
                print(f"🔍 LLM原始响应前500字符: {response[:500]}")

                # 尝试提取JSON部分
                response_clean = response.strip()

                # 如果响应包含代码块，提取其中的JSON
                if "```json" in response_clean:
                    json_start = response_clean.find("```json") + 7
                    json_end = response_clean.find("```", json_start)
                    if json_end != -1:
                        response_clean = response_clean[json_start:json_end].strip()
                        print("🔍 从```json代码块中提取JSON")
                elif "```" in response_clean:
                    # 处理没有语言标识的代码块
                    json_start = response_clean.find("```") + 3
                    json_end = response_clean.find("```", json_start)
                    if json_end != -1:
                        response_clean = response_clean[json_start:json_end].strip()
                        print("🔍 从```代码块中提取JSON")

                # 尝试找到JSON对象的开始和结束
                json_start = response_clean.find('{')
                json_end = response_clean.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    response_clean = response_clean[json_start:json_end]
                    print("🔍 提取JSON对象部分")

                print(f"🔍 清理后的JSON长度: {len(response_clean)}")
                print(f"🔍 清理后的JSON内容: {response_clean}")

                # 尝试解析JSON
                strategy = json.loads(response_clean)
                print(f"✅ 策略生成成功: {strategy.get('reasoning', '智能策略')}")
                return strategy

            except json.JSONDecodeError as e:
                print(f"⚠️ 策略解析失败: {e}")
                print(f"🔍 解析失败的原始响应: {response}")
                print(f"🔍 解析失败的清理内容: {response_clean if 'response_clean' in locals() else 'N/A'}")
                return self._get_default_strategy(url, user_intent)
            except Exception as e:
                print(f"⚠️ 策略解析异常: {e}")
                return self._get_default_strategy(url, user_intent)

        except Exception as e:
            print(f"⚠️ 策略生成失败: {e}，使用默认策略")
            return self._get_default_strategy(url, user_intent)

    def _get_default_strategy(self, url: str, user_intent: str) -> Dict[str, Any]:
        """获取默认爬取策略"""
        # 基于URL和用户意图的简单策略
        if any(keyword in user_intent.lower() for keyword in ['新闻', '文章', 'news', 'article']):
            return {
                'extract_type': 'markdown',
                'css_selector': 'article, .article, .content, .post',
                'content_filter': 'bm25',
                'filter_threshold': 0.5,
                'reasoning': '检测到新闻/文章需求，使用内容提取策略'
            }
        elif any(keyword in user_intent.lower() for keyword in ['产品', '价格', 'product', 'price']):
            return {
                'extract_type': 'structured',
                'css_selector': '.product, .item',
                'extraction_schema': {
                    'name': 'Product Info',
                    'fields': [
                        {'name': 'title', 'selector': 'h1, .title', 'type': 'text'},
                        {'name': 'price', 'selector': '.price', 'type': 'text'},
                        {'name': 'description', 'selector': '.description', 'type': 'text'}
                    ]
                },
                'content_filter': 'pruning',
                'filter_threshold': 0.3,
                'reasoning': '检测到产品信息需求，使用结构化提取策略'
            }
        else:
            return {
                'extract_type': 'markdown',
                'css_selector': 'main, .main, .content, body',
                'content_filter': 'pruning',
                'filter_threshold': 0.48,
                'reasoning': '使用通用内容提取策略'
            }

    async def _crawl_with_strategy(self, url: str, extract_type: str = "markdown",
                                 css_selector: str = "", extraction_schema: dict = None,
                                 js_code: str = "", wait_for: str = "",
                                 content_filter: str = "pruning", filter_threshold: float = 0.48,
                                 user_query: str = "") -> str:
        """使用指定策略执行爬取"""
        try:
            from crawl4ai import AsyncWebCrawler
            from crawl4ai.extraction_strategy import LLMExtractionStrategy, CosineStrategy

            # 配置爬取参数
            crawler_config = {
                'headless': True,
                'verbose': False
            }

            async with AsyncWebCrawler(**crawler_config) as crawler:
                # 构建爬取参数
                crawl_params = {
                    'url': url,
                    'word_count_threshold': 10,
                    'bypass_cache': True,
                    'css_selector': css_selector if css_selector else None,
                    'wait_for': wait_for if wait_for else None,
                    'js_code': js_code if js_code else None
                }

                # 设置提取策略
                if extract_type == "structured" and extraction_schema:
                    # 使用项目的LLM配置进行结构化提取
                    provider = self.agent.config.utility_model.provider.value.lower()
                    api_key = self._get_api_key_for_provider(provider)

                    # 映射提供商名称到crawl4ai支持的格式
                    provider_mapping = {
                        'openai': 'openai',
                        'openai azure': 'openai',
                        'anthropic': 'anthropic',
                        'groq': 'groq',
                        'deepseek': 'openai',  # DeepSeek使用OpenAI兼容接口
                        'openrouter': 'openai',  # OpenRouter使用OpenAI兼容接口
                        'sambanova': 'openai',  # Sambanova使用OpenAI兼容接口
                        'siliconflow': 'openai',  # SiliconFlow使用OpenAI兼容接口
                        'volcengine': 'openai',  # VolcEngine使用OpenAI兼容接口
                        'other': 'openai'  # 其他提供商默认使用OpenAI兼容接口
                    }

                    crawl4ai_provider = provider_mapping.get(provider, 'openai')

                    extraction_strategy = LLMExtractionStrategy(
                        provider=crawl4ai_provider,
                        api_token=api_key,
                        schema=extraction_schema,
                        extraction_type="schema",
                        instruction=f"Extract information according to the schema. User intent: {user_query}"
                    )
                    crawl_params['extraction_strategy'] = extraction_strategy

                # 设置内容过滤
                if content_filter == "bm25" and user_query:
                    crawl_params['content_filter'] = CosineStrategy(
                        semantic_filter=user_query,
                        word_count_threshold=10,
                        max_dist=0.2,
                        linkage_method='ward',
                        top_k=3,
                        model_name='BAAI/bge-small-en-v1.5'
                    )

                print(f"🔄 执行爬取: {url}")
                result = await crawler.arun(**crawl_params)

                if result.success:
                    # 格式化结果
                    if extract_type == "structured" and result.extracted_content:
                        content = self._format_structured_result(result.extracted_content, url)
                    elif extract_type == "text":
                        content = self._format_text_result(result.cleaned_html, url)
                    else:  # markdown
                        content = self._format_markdown_result(result.markdown, url)

                    return content
                else:
                    return f"爬取失败: {result.error_message}"

        except Exception as e:
            return f"爬取执行失败: {str(e)}"

    def _get_api_key_for_provider(self, provider: str) -> str:
        """根据提供商获取对应的API密钥"""
        try:
            from models import get_api_key

            # 提供商名称映射
            provider_key_mapping = {
                'openai': 'openai',
                'openai azure': 'openai_azure',
                'anthropic': 'anthropic',
                'groq': 'groq',
                'deepseek': 'deepseek',
                'openrouter': 'openrouter',
                'sambanova': 'sambanova',
                'siliconflow': 'siliconflow',
                'volcengine': 'volcengine',
                'mistral ai': 'mistral',
                'google': 'google',
                'huggingface': 'huggingface'
            }

            key_name = provider_key_mapping.get(provider, 'openai')
            api_key = get_api_key(key_name)

            # 如果获取失败，尝试从环境变量获取
            if api_key == "None" or not api_key:
                import os
                fallback_keys = [
                    f"API_KEY_{key_name.upper()}",
                    f"{key_name.upper()}_API_KEY",
                    "OPENAI_API_KEY"  # 最后回退到OpenAI
                ]

                for fallback_key in fallback_keys:
                    api_key = os.getenv(fallback_key)
                    if api_key:
                        break

                if not api_key:
                    api_key = "none"  # 默认值，某些本地模型不需要API密钥

            return api_key

        except Exception as e:
            print(f"⚠️ 获取API密钥失败: {e}，使用默认值")
            return "none"

    def _format_structured_result(self, extracted_content: str, url: str) -> str:
        """格式化结构化结果"""
        try:
            data = json.loads(extracted_content) if isinstance(extracted_content, str) else extracted_content
            
            formatted_result = f"""
🕷️ 智能网页爬取结果 (结构化数据)
{'='*50}

📍 源URL: {url}
📊 提取类型: 结构化数据

📋 提取结果:
{json.dumps(data, ensure_ascii=False, indent=2)}

✅ 爬取完成！
"""
            return formatted_result.strip()
        except:
            return f"结构化数据解析失败: {extracted_content}"

    def _format_text_result(self, content: str, url: str) -> str:
        """格式化文本结果"""
        formatted_result = f"""
🕷️ 智能网页爬取结果 (纯文本)
{'='*50}

📍 源URL: {url}
📊 提取类型: 纯文本

📄 内容:
{content[:2000]}{'...' if len(content) > 2000 else ''}

✅ 爬取完成！内容长度: {len(content)} 字符
"""
        return formatted_result.strip()

    def _format_markdown_result(self, content: str, url: str) -> str:
        """格式化Markdown结果"""
        formatted_result = f"""
🕷️ 智能网页爬取结果 (Markdown)
{'='*50}

📍 源URL: {url}
📊 提取类型: Markdown格式

📝 内容:
{content[:2000]}{'...' if len(content) > 2000 else ''}

✅ 爬取完成！内容长度: {len(content)} 字符
"""
        return formatted_result.strip()

import asyncio
from datetime import datetime
import time
from python.helpers.task_scheduler import TaskScheduler
from python.helpers.print_style import PrintStyle
from python.helpers import errors
from python.helpers import runtime


SLEEP_TIME = 60

# 调度器日志配置
SCHEDULER_VERBOSE_LOGGING = False  # 设置为True显示详细日志，False只显示重要事件
SCHEDULER_LOG_INTERVAL = 0  # 无任务时的日志间隔（秒），0表示不显示

keep_running = True
pause_time = 0
last_status_log_time = 0


async def run_loop():
    global pause_time, keep_running, last_status_log_time

    # 添加启动日志
    printer = PrintStyle(italic=True, font_color="cyan", padding=False)
    printer.print("🔄 Job Loop starting...")
    printer.print(f"   Development mode: {runtime.is_development()}")
    printer.print(f"   Sleep time: {SLEEP_TIME} seconds")
    printer.print(f"   Verbose logging: {SCHEDULER_VERBOSE_LOGGING}")
    printer.print(f"   Status log interval: {SCHEDULER_LOG_INTERVAL}s (0=disabled)")

    # 在本地开发模式下，不需要暂停job_loop，因为没有容器重复运行的问题
    if runtime.is_development():
        printer.print("🔧 Development mode: Job Loop will run locally (no pause needed)")

    while True:

        if not keep_running and (time.time() - pause_time) > (SLEEP_TIME * 2):
            printer.print("▶️  Job Loop resuming after pause timeout")
            resume_loop()

        if keep_running:
            try:
                await scheduler_tick()
            except Exception as e:
                PrintStyle().error(errors.format_error(e))
        else:
            printer.print(f"⏸️  Job Loop paused, waiting... (paused for {int(time.time() - pause_time)}s)")

        await asyncio.sleep(SLEEP_TIME)  # TODO! - if we lower it under 1min, it can run a 5min job multiple times in it's target minute


async def scheduler_tick():
    global last_status_log_time

    # Get the task scheduler instance and print detailed debug info
    printer = PrintStyle(italic=True, font_color="green", padding=False)

    try:
        scheduler = TaskScheduler.get()
        await scheduler.reload()

        # 获取任务信息用于调试
        tasks = scheduler.get_tasks()
        due_tasks = await scheduler._tasks.get_due_tasks()

        current_time = time.time()
        should_log_status = False

        # 决定是否显示状态日志
        if SCHEDULER_VERBOSE_LOGGING:
            # 详细模式：总是显示
            should_log_status = True
        elif due_tasks:
            # 有到期任务时总是显示
            should_log_status = True
        elif SCHEDULER_LOG_INTERVAL > 0 and (current_time - last_status_log_time) >= SCHEDULER_LOG_INTERVAL:
            # 定期状态日志（如果启用）
            should_log_status = True
            last_status_log_time = current_time

        if should_log_status:
            if SCHEDULER_VERBOSE_LOGGING:
                printer.print(f"🔄 Scheduler tick at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            printer.print(f"📊 Scheduler status: {len(tasks)} total tasks, {len(due_tasks)} due tasks")

        if due_tasks:
            for task in due_tasks:
                printer.print(f"   ⏰ Due task: {task.name} (UUID: {task.uuid[:8]}...)")

        # Run the scheduler tick
        await scheduler.tick()

        if due_tasks:
            printer.print(f"✅ Scheduler tick completed, processed {len(due_tasks)} due tasks")

    except Exception as e:
        printer.print(f"❌ Scheduler tick failed: {str(e)}")
        raise


def pause_loop():
    global keep_running, pause_time
    printer = PrintStyle(italic=True, font_color="yellow", padding=False)
    printer.print("⏸️  Job Loop pause requested")
    keep_running = False
    pause_time = time.time()


def resume_loop():
    global keep_running, pause_time
    printer = PrintStyle(italic=True, font_color="green", padding=False)
    printer.print("▶️  Job Loop resume requested")
    keep_running = True
    pause_time = 0

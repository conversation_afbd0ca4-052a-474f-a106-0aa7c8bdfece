import argparse
import inspect
from typing import <PERSON>V<PERSON>, Callable, Awaitable, Union, overload, cast
from python.helpers import dotenv, settings
import asyncio
import threading
import queue

T = TypeVar('T')
R = TypeVar('R')

parser = argparse.ArgumentParser()
args = {}
# Docker disabled for local development
# dockerman = None


def initialize():
    global args
    if args:
        return
    parser.add_argument("--port", type=int, default=None, help="Web UI port")
    parser.add_argument("--host", type=str, default=None, help="Web UI host")
    parser.add_argument(
        "--cloudflare_tunnel",
        type=bool,
        default=False,
        help="Use cloudflare tunnel for public URL",
    )
    parser.add_argument(
        "--development", type=bool, default=False, help="Development mode"
    )

    known, unknown = parser.parse_known_args()
    args = vars(known)
    for arg in unknown:
        if "=" in arg:
            key, value = arg.split("=", 1)
            key = key.lstrip("-")
            args[key] = value


def get_arg(name: str):
    global args
    return args.get(name, None)

def has_arg(name: str):
    global args
    return name in args

def is_dockerized() -> bool:
    """Always return False for local development mode."""
    return False

def is_development() -> bool:
    """Always return True for local development mode."""
    return True

def get_local_url():
    """Always return localhost for local development mode."""
    return "127.0.0.1"

@overload
async def call_development_function(func: Callable[..., Awaitable[T]], *args, **kwargs) -> T: ...

@overload
async def call_development_function(func: Callable[..., T], *args, **kwargs) -> T: ...

async def call_development_function(func: Union[Callable[..., T], Callable[..., Awaitable[T]]], *args, **kwargs) -> T:
    """
    Execute function locally (RFC mechanism removed for local development).
    This function now always executes locally regardless of development mode.
    """
    if inspect.iscoroutinefunction(func):
        return await func(*args, **kwargs)
    else:
        return func(*args, **kwargs) # type: ignore


def call_development_function_sync(func: Union[Callable[..., T], Callable[..., Awaitable[T]]], *args, **kwargs) -> T:
    """
    Execute function synchronously (RFC mechanism removed for local development).
    This function now always executes locally.
    """
    if inspect.iscoroutinefunction(func):
        # run async function in sync manner
        result_queue = queue.Queue()

        def run_in_thread():
            result = asyncio.run(call_development_function(func, *args, **kwargs))
            result_queue.put(result)

        thread = threading.Thread(target=run_in_thread)
        thread.start()
        thread.join(timeout=30)  # wait for thread with timeout

        if thread.is_alive():
            raise TimeoutError("Function call timed out after 30 seconds")

        result = result_queue.get_nowait()
        return cast(T, result)
    else:
        # Direct synchronous execution
        return func(*args, **kwargs) # type: ignore


def get_web_ui_port():
    web_ui_port = (
        get_arg("port")
        or int(dotenv.get_dotenv_value("WEB_UI_PORT", 0))
        or 5000
    )
    return web_ui_port

def get_tunnel_api_port():
    tunnel_api_port = (
        get_arg("tunnel_api_port")
        or int(dotenv.get_dotenv_value("TUNNEL_API_PORT", 0))
        or 55520
    )
    return tunnel_api_port
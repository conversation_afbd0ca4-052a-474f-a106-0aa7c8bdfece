# PocketFlow 与 Agent Zero 的适配器
import yaml
from typing import Dict, Any
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate


class AgentZeroLLMAdapter:
    """将 Agent Zero 的 LLM 调用接口适配给 PocketFlow 使用"""
    
    def __init__(self, agent):
        self.agent = agent
    
    async def call_llm(self, prompt: str, system_prompt: str = "") -> str:
        """
        统一的 LLM 调用接口，适配 Agent Zero 的调用方式
        """
        try:
            if system_prompt:
                # 使用 utility_model 进行系统级调用
                response = await self.agent.call_utility_model(
                    system=system_prompt,
                    message=prompt
                )
            else:
                # 使用 utility_model 进行简单调用，提供默认系统提示
                response = await self.agent.call_utility_model(
                    system="你是一个有用的AI助手，请根据用户的要求提供准确的回答。",
                    message=prompt
                )

            return response
        except Exception as e:
            print(f"❌ LLM调用错误: {str(e)}")
            return f"LLM调用错误: {str(e)}"


def call_llm(prompt: str, system_prompt: str = "", agent=None) -> str:
    """
    同步版本的 LLM 调用，用于兼容 PocketFlow 示例
    注意：这是简化版本，实际使用时建议使用异步版本
    """
    if not agent:
        raise ValueError("需要提供 agent 实例")
    
    # 这里可以使用 asyncio.run() 或其他方式处理异步调用
    # 为了简化，这里返回一个模拟响应
    return f"模拟LLM响应: {prompt[:50]}..."


class PocketFlowCodeExecutor:
    """代码执行适配器，使用 Agent Zero 的代码执行能力"""
    
    def __init__(self, agent):
        self.agent = agent
    
    async def execute_python(self, code: str, inputs: Dict[str, Any]) -> tuple[Any, str]:
        """
        执行 Python 代码并返回结果
        """
        try:
            # 构建完整的代码，包含输入参数
            full_code = self._build_code_with_inputs(code, inputs)

            print(f"🔧 执行代码: {full_code[:100]}...")

            # 使用 Agent Zero 的代码执行工具
            from python.tools.code_execution_tool import CodeExecution

            code_tool = CodeExecution(
                agent=self.agent,
                name="code_execution_tool",
                method="",
                args={"runtime": "python", "code": full_code, "session": 0},
                message=""
            )

            result = await code_tool.execute()

            # 解析结果
            if result and hasattr(result, 'message'):
                # 尝试从输出中提取实际结果
                output = self._parse_execution_result(result.message)
                print(f"✅ 代码执行成功: {output}")
                return output, None
            else:
                print("❌ 代码执行失败：无返回结果")
                return None, "代码执行失败"

        except Exception as e:
            print(f"❌ 代码执行异常: {str(e)}")
            return None, str(e)
    
    def _build_code_with_inputs(self, function_code: str, inputs: Dict[str, Any]) -> str:
        """构建包含输入参数的完整代码"""
        # 构建参数字符串
        params = ", ".join([f"{k}={repr(v)}" for k, v in inputs.items()])
        
        full_code = f"""
{function_code}

# 执行函数
try:
    result = run_code({params})
    print(f"RESULT: {{result}}")
except Exception as e:
    print(f"ERROR: {{e}}")
"""
        return full_code
    
    def _parse_execution_result(self, output: str) -> Any:
        """从执行输出中解析实际结果"""
        lines = output.split('\n')
        for line in lines:
            if line.startswith("RESULT: "):
                result_str = line[8:]  # 移除 "RESULT: " 前缀
                try:
                    # 尝试解析为 Python 对象
                    return eval(result_str)
                except:
                    return result_str
        return None


def execute_python(code: str, inputs: Dict[str, Any]) -> tuple[Any, str]:
    """
    同步版本的代码执行，用于兼容 PocketFlow 示例
    """
    # 简化版本，实际使用时需要传入 agent 实例
    try:
        # 这里可以实现简单的代码执行逻辑
        # 或者抛出异常提示需要异步版本
        return None, "需要使用异步版本的代码执行"
    except Exception as e:
        return None, str(e)


class YAMLParser:
    """YAML 解析工具，增强错误处理"""
    
    @staticmethod
    def safe_load(yaml_str: str) -> Dict[str, Any]:
        """安全加载 YAML 字符串"""
        try:
            return yaml.safe_load(yaml_str)
        except yaml.YAMLError as e:
            raise ValueError(f"YAML 解析错误: {e}")
    
    @staticmethod
    def extract_yaml_from_response(response: str) -> str:
        """从 LLM 响应中提取 YAML 内容"""
        # 查找 ```yaml 和 ``` 之间的内容
        start_marker = "```yaml"
        end_marker = "```"
        
        start_idx = response.find(start_marker)
        if start_idx == -1:
            raise ValueError("响应中未找到 YAML 代码块")
        
        start_idx += len(start_marker)
        end_idx = response.find(end_marker, start_idx)
        
        if end_idx == -1:
            raise ValueError("YAML 代码块未正确关闭")
        
        return response[start_idx:end_idx].strip()

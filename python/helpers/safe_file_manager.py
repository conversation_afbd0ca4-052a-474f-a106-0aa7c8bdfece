"""
安全文件管理器 - 为项目提供安全的文件保存路径管理
"""
import os
from pathlib import Path
from python.helpers import files
from python.helpers.print_style import PrintStyle


class SafeFileManager:
    """安全文件管理器，提供安全的文件保存路径"""
    
    # 安全的输出目录配置
    SAFE_DIRS = {
        'reports': 'tmp/reports',      # HTML报告
        'downloads': 'tmp/downloads',   # 下载文件
        'outputs': 'work_dir',         # 代码执行输出
        'temp': 'tmp',                 # 临时文件
    }
    
    @classmethod
    def get_safe_path(cls, category: str, filename: str) -> str:
        """
        获取安全的文件保存路径
        
        Args:
            category: 文件类别 ('reports', 'downloads', 'outputs', 'temp')
            filename: 文件名
            
        Returns:
            安全的绝对文件路径
        """
        if category not in cls.SAFE_DIRS:
            category = 'temp'
            
        # 获取目录路径
        dir_path = files.get_abs_path(cls.SAFE_DIRS[category])
        
        # 确保目录存在
        os.makedirs(dir_path, exist_ok=True)
        
        # 清理文件名，防止路径遍历攻击
        safe_filename = cls._sanitize_filename(filename)
        
        # 返回完整路径
        full_path = os.path.join(dir_path, safe_filename)
        
        PrintStyle.info(f"Safe file path: {full_path}")
        return full_path
    
    @classmethod
    def _sanitize_filename(cls, filename: str) -> str:
        """清理文件名，移除不安全字符"""
        # 移除路径分隔符和其他不安全字符
        unsafe_chars = ['/', '\\', '..', '<', '>', ':', '"', '|', '?', '*']
        safe_name = filename
        
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')
            
        # 限制文件名长度
        if len(safe_name) > 200:
            name_part, ext_part = os.path.splitext(safe_name)
            safe_name = name_part[:200-len(ext_part)] + ext_part
            
        return safe_name
    
    @classmethod
    def ensure_directories(cls):
        """确保所有安全目录存在"""
        for category, dir_path in cls.SAFE_DIRS.items():
            full_path = files.get_abs_path(dir_path)
            os.makedirs(full_path, exist_ok=True)
            PrintStyle.info(f"Ensured directory exists: {full_path}")
    
    @classmethod
    def get_relative_path(cls, category: str, filename: str) -> str:
        """获取相对于项目根目录的路径"""
        if category not in cls.SAFE_DIRS:
            category = 'temp'
            
        safe_filename = cls._sanitize_filename(filename)
        return os.path.join(cls.SAFE_DIRS[category], safe_filename)

"""
Tool Selector - 新增工具选择器
温和推荐策略，只推荐新增工具，不干预原生工具
"""
import re
from typing import Dict, List, Tuple, Optional


class NewToolSelector:
    """新增工具选择器 - 温和推荐策略"""

    def __init__(self):
        # 深度搜索关键词 - 触发enhanced_search_engine
        self.deep_search_keywords = [
            "深入", "详细", "全面", "研究", "深度", "彻底",
            "完整", "系统性", "综合", "专业", "学术", "权威",
            # 英文关键词
            "enhance", "enhanced", "deep", "detailed", "comprehensive",
            "thorough", "research", "in-depth", "extensive", "complete",
            "systematic", "professional", "academic", "authoritative"
        ]

        # 结构化思维关键词 - 触发sequential_thinking
        self.thinking_keywords = [
            "系统", "结构", "分步", "逻辑", "分析", "框架",
            "步骤", "流程", "方法论", "思路", "推理", "论证",
            # 英文关键词
            "systematic", "structured", "logical", "analysis", "framework",
            "step", "process", "methodology", "reasoning", "thinking"
        ]

        # 网页爬取关键词 - 触发web_crawler
        self.crawler_keywords = [
            "爬取", "抓取", "采集", "收集", "获取", "提取", "数据采集",
            "网页内容", "网站数据", "页面信息", "内容提取", "信息收集",
            "网站内容", "网页数据", "页面内容", "网站信息", "网页信息",
            # 英文关键词
            "crawl", "scrape", "extract", "collect", "gather", "fetch",
            "web scraping", "data extraction", "content extraction",
            "website content", "web content", "page content", "site data"
        ]

        # 置信度阈值
        self.confidence_threshold = 0.7  # 70%置信度

    def analyze_user_input(self, user_input) -> Dict:
        """分析用户输入，判断是否需要推荐新工具"""
        # 处理不同类型的输入
        if hasattr(user_input, 'content'):
            # 如果是Message对象，提取content
            text_input = str(user_input.content)
        elif isinstance(user_input, str):
            # 如果是字符串，直接使用
            text_input = user_input
        else:
            # 其他类型，转换为字符串
            text_input = str(user_input)

        user_input_lower = text_input.lower()

        # 检测深度搜索需求
        deep_search_score = self._calculate_keyword_score(
            user_input_lower, self.deep_search_keywords
        )

        # 检测结构化思维需求
        thinking_score = self._calculate_keyword_score(
            user_input_lower, self.thinking_keywords
        )

        # 检测网页爬取需求
        crawler_score = self._calculate_keyword_score(
            user_input_lower, self.crawler_keywords
        )

        # 分析结果
        analysis = {
            'enhanced_search_engine': {
                'score': deep_search_score,
                'recommended': deep_search_score >= self.confidence_threshold,
                'confidence': min(deep_search_score, 1.0),
                'trigger_keywords': self._find_matching_keywords(
                    user_input_lower, self.deep_search_keywords
                )
            },
            'sequential_thinking': {
                'score': thinking_score,
                'recommended': thinking_score >= self.confidence_threshold,
                'confidence': min(thinking_score, 1.0),
                'trigger_keywords': self._find_matching_keywords(
                    user_input_lower, self.thinking_keywords
                )
            },
            'web_crawler': {
                'score': crawler_score,
                'recommended': crawler_score >= self.confidence_threshold,
                'confidence': min(crawler_score, 1.0),
                'trigger_keywords': self._find_matching_keywords(
                    user_input_lower, self.crawler_keywords
                )
            }
        }

        return analysis

    def _calculate_keyword_score(self, text: str, keywords: List[str]) -> float:
        """计算关键词匹配得分"""
        score = 0.0
        text_words = text.split()

        for keyword in keywords:
            # 直接匹配
            if keyword in text:
                score += 0.3

            # 词根匹配
            for word in text_words:
                if keyword in word or word in keyword:
                    score += 0.2

        # 多个关键词出现时增加权重
        matching_keywords = self._find_matching_keywords(text, keywords)
        if len(matching_keywords) > 1:
            score += 0.2 * (len(matching_keywords) - 1)

        # 特定短语匹配
        high_value_phrases = {
            'enhanced_search_engine': [
                "深入研究", "详细分析", "全面了解", "系统性搜索",
                "专业资料", "权威信息", "学术研究", "深度调研",
                # 英文短语
                "enhance search", "enhanced search", "deep research", "detailed analysis",
                "comprehensive study", "thorough investigation", "in-depth analysis",
                "systematic search", "professional research", "academic research"
            ],
            'sequential_thinking': [
                "系统分析", "结构化思考", "逻辑推理", "分步骤",
                "方法论", "思维框架", "分析框架", "解决方案",
                # 英文短语
                "sequential thinking", "sequential analysis", "systematic analysis",
                "structured thinking", "logical reasoning", "step by step",
                "methodology", "thinking framework", "analytical framework"
            ],
            'web_crawler': [
                "网页爬取", "数据采集", "内容提取", "信息收集",
                "网站数据", "页面信息", "批量获取", "自动抓取",
                "爬取网站", "爬取内容", "抓取网页", "抓取数据",
                "收集网站", "采集网页", "获取网站", "提取网页",
                "爬取这个", "抓取这个", "收集这个", "采集这个", "获取这个",
                "网站的内容", "网页的内容", "页面的内容", "网站内容",
                # 英文短语
                "web crawling", "web scraping", "data extraction", "content scraping",
                "website data", "page content", "batch collection", "automated scraping",
                "crawl website", "scrape website", "extract website", "collect website",
                "crawl this", "scrape this", "extract this", "collect this"
            ]
        }

        # 检查是否包含高价值短语
        for tool_name, phrases in high_value_phrases.items():
            for phrase in phrases:
                if phrase in text:
                    if tool_name == 'enhanced_search_engine' and keywords == self.deep_search_keywords:
                        score += 0.4
                    elif tool_name == 'sequential_thinking' and keywords == self.thinking_keywords:
                        score += 0.4
                    elif tool_name == 'web_crawler' and keywords == self.crawler_keywords:
                        score += 0.4

        return min(score, 1.0)  # 限制最大值为1.0

    def _find_matching_keywords(self, text: str, keywords: List[str]) -> List[str]:
        """找到匹配的关键词"""
        matching = []
        for keyword in keywords:
            if keyword in text:
                matching.append(keyword)
        return matching

    def generate_recommendation_message(self, analysis: Dict) -> Optional[str]:
        """生成推荐消息"""
        recommendations = []

        # 检查enhanced_search_engine推荐
        if analysis['enhanced_search_engine']['recommended']:
            confidence = analysis['enhanced_search_engine']['confidence']
            keywords = analysis['enhanced_search_engine']['trigger_keywords']

            message = f"""
💡 检测到您可能需要深度搜索分析（置信度: {confidence:.1%}）
   触发关键词: {', '.join(keywords)}

   建议使用 enhanced_search_engine 工具，它可以提供：
   • 多轮搜索策略
   • 结果质量评估
   • 智能摘要生成
   • 更全面的信息收集
"""
            recommendations.append(message.strip())

        # 检查sequential_thinking推荐
        if analysis['sequential_thinking']['recommended']:
            confidence = analysis['sequential_thinking']['confidence']
            keywords = analysis['sequential_thinking']['trigger_keywords']

            message = f"""
💡 检测到您可能需要结构化分析（置信度: {confidence:.1%}）
   触发关键词: {', '.join(keywords)}

   建议使用 sequential_thinking 工具，它可以提供：
   • 问题系统性分解
   • 逐步逻辑推理
   • 结构化分析框架
   • 综合结论整合
"""
            recommendations.append(message.strip())

        # 检查web_crawler推荐
        if analysis['web_crawler']['recommended']:
            confidence = analysis['web_crawler']['confidence']
            keywords = analysis['web_crawler']['trigger_keywords']

            message = f"""
💡 检测到您可能需要智能网页爬取（置信度: {confidence:.1%}）
   触发关键词: {', '.join(keywords)}

   建议使用 web_crawler 工具，它可以提供：
   • LLM自主生成爬取策略
   • 智能网站类型识别
   • 多格式内容提取
   • 高性能异步处理
"""
            recommendations.append(message.strip())

        if recommendations:
            header = "🔧 智能工具推荐"
            footer = "💭 这些是基于您的需求的温和建议，您可以选择使用或继续使用常规工具。"

            full_message = f"""
{header}
{'='*30}

{chr(10).join(recommendations)}

{footer}
"""
            return full_message.strip()

        return None

    def should_recommend_tools(self, user_input) -> bool:
        """判断是否应该推荐新工具"""
        analysis = self.analyze_user_input(user_input)

        return (analysis['enhanced_search_engine']['recommended'] or
                analysis['sequential_thinking']['recommended'] or
                analysis['web_crawler']['recommended'])

    def get_recommended_tools(self, user_input) -> List[str]:
        """获取推荐的工具列表，按置信度排序"""
        analysis = self.analyze_user_input(user_input)
        tool_scores = []

        if analysis['enhanced_search_engine']['recommended']:
            tool_scores.append(('enhanced_search_engine', analysis['enhanced_search_engine']['confidence']))

        if analysis['sequential_thinking']['recommended']:
            tool_scores.append(('sequential_thinking', analysis['sequential_thinking']['confidence']))

        if analysis['web_crawler']['recommended']:
            tool_scores.append(('web_crawler', analysis['web_crawler']['confidence']))

        # 按置信度降序排序
        tool_scores.sort(key=lambda x: x[1], reverse=True)

        return [tool for tool, score in tool_scores]

    def get_tool_description(self, tool_name: str) -> str:
        """获取工具描述"""
        descriptions = {
            'enhanced_search_engine': """
增强搜索引擎 - 深度研究专用工具
• 执行多轮搜索策略（基础搜索 + 扩展关键词 + 相关主题）
• 智能结果质量评估和排序
• 生成综合性摘要和建议
• 适用于需要全面、深入信息的研究场景
""".strip(),

            'sequential_thinking': """
序列化思维工具 - 结构化分析专用
• 系统性问题分解和分析
• 多维度结构化思考框架
• 逐步逻辑推理和论证
• 综合结论整合和行动建议
• 适用于复杂问题的系统性分析
""".strip(),

            'web_crawler': """
智能网页爬虫 - 网页内容提取专用
• LLM自主生成最优爬取策略
• 智能识别网站类型和结构
• 支持多种格式内容提取
• 高性能异步处理和智能过滤
• 适用于各种网页内容采集需求
""".strip()
        }

        return descriptions.get(tool_name, "工具描述暂不可用")


# 全局工具选择器实例
tool_selector = NewToolSelector()


def analyze_user_request(user_input) -> Dict:
    """分析用户请求的便捷函数"""
    return tool_selector.analyze_user_input(user_input)


def get_tool_recommendations(user_input) -> Optional[str]:
    """获取工具推荐的便捷函数"""
    return tool_selector.generate_recommendation_message(
        tool_selector.analyze_user_input(user_input)
    )


def should_suggest_new_tools(user_input) -> bool:
    """判断是否应该建议新工具的便捷函数"""
    return tool_selector.should_recommend_tools(user_input)

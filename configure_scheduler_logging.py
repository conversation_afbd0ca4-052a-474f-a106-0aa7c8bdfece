#!/usr/bin/env python3
"""
调度器日志配置工具
用于快速切换调度器日志显示模式
"""

import sys
import re
import os

def read_job_loop_file():
    """读取job_loop.py文件内容"""
    file_path = "python/helpers/job_loop.py"
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"❌ 文件未找到: {file_path}")
        return None

def write_job_loop_file(content):
    """写入job_loop.py文件内容"""
    file_path = "python/helpers/job_loop.py"
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"❌ 写入文件失败: {e}")
        return False

def get_current_config(content):
    """获取当前配置"""
    verbose_match = re.search(r'SCHEDULER_VERBOSE_LOGGING = (True|False)', content)
    interval_match = re.search(r'SCHEDULER_LOG_INTERVAL = (\d+)', content)
    
    if verbose_match and interval_match:
        verbose = verbose_match.group(1) == 'True'
        interval = int(interval_match.group(1))
        return verbose, interval
    return None, None

def update_config(content, verbose, interval):
    """更新配置"""
    # 更新 SCHEDULER_VERBOSE_LOGGING
    content = re.sub(
        r'SCHEDULER_VERBOSE_LOGGING = (True|False)',
        f'SCHEDULER_VERBOSE_LOGGING = {verbose}',
        content
    )
    
    # 更新 SCHEDULER_LOG_INTERVAL
    content = re.sub(
        r'SCHEDULER_LOG_INTERVAL = \d+',
        f'SCHEDULER_LOG_INTERVAL = {interval}',
        content
    )
    
    return content

def show_current_config():
    """显示当前配置"""
    content = read_job_loop_file()
    if content is None:
        return
    
    verbose, interval = get_current_config(content)
    if verbose is None:
        print("❌ 无法读取当前配置")
        return
    
    print("📊 当前调度器日志配置:")
    print(f"   详细日志模式: {verbose}")
    print(f"   状态日志间隔: {interval}秒 {'(关闭)' if interval == 0 else ''}")
    print()
    
    # 显示模式说明
    if verbose:
        print("🔍 当前模式: 详细模式")
        print("   - 每60秒显示完整的调度器信息")
        print("   - 包括时间戳和状态信息")
    elif interval == 0:
        print("🔇 当前模式: 完全静默")
        print("   - 只在有任务执行时显示日志")
        print("   - 无任务时不显示任何状态信息")
    else:
        print("📅 当前模式: 定期状态")
        print(f"   - 有任务时立即显示")
        print(f"   - 无任务时每{interval}秒显示状态")

def set_config_mode(mode):
    """设置配置模式"""
    content = read_job_loop_file()
    if content is None:
        return False
    
    if mode == "silent":
        verbose, interval = False, 0
        mode_name = "完全静默"
    elif mode == "periodic":
        verbose, interval = False, 300
        mode_name = "定期状态（5分钟）"
    elif mode == "verbose":
        verbose, interval = True, 60
        mode_name = "详细模式"
    else:
        print(f"❌ 未知模式: {mode}")
        return False
    
    new_content = update_config(content, verbose, interval)
    
    if write_job_loop_file(new_content):
        print(f"✅ 已设置为 {mode_name} 模式")
        print("⚠️  需要重启服务才能生效:")
        print("   ./quick_start.sh --stop")
        print("   ./quick_start.sh")
        return True
    else:
        return False

def show_help():
    """显示帮助信息"""
    print("🔧 调度器日志配置工具")
    print()
    print("用法:")
    print("  python configure_scheduler_logging.py [选项]")
    print()
    print("选项:")
    print("  --show          显示当前配置")
    print("  --silent        设置为完全静默模式（推荐）")
    print("  --periodic      设置为定期状态模式（5分钟间隔）")
    print("  --verbose       设置为详细模式（每60秒显示）")
    print("  --help          显示此帮助信息")
    print()
    print("模式说明:")
    print("  🔇 完全静默:   只在有任务执行时显示，无噪音")
    print("  📅 定期状态:   有任务时立即显示，无任务时每5分钟显示状态")
    print("  🔍 详细模式:   每60秒显示完整调度器信息，用于调试")
    print()
    print("示例:")
    print("  python configure_scheduler_logging.py --show")
    print("  python configure_scheduler_logging.py --silent")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    arg = sys.argv[1]
    
    if arg == "--show":
        show_current_config()
    elif arg == "--silent":
        set_config_mode("silent")
    elif arg == "--periodic":
        set_config_mode("periodic")
    elif arg == "--verbose":
        set_config_mode("verbose")
    elif arg == "--help":
        show_help()
    else:
        print(f"❌ 未知选项: {arg}")
        print("使用 --help 查看帮助信息")

if __name__ == "__main__":
    main()

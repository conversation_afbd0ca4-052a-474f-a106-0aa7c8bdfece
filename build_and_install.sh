#!/bin/bash

# Agent-Zero Android应用构建和安装脚本

echo "🚀 === Agent-Zero Android应用构建脚本 ==="
echo ""

# 配置变量
APP_NAME="agent-zero-mobile"
BUILD_TYPE="release"  # debug 或 release
INSTALL_METHOD="adb"  # adb 或 manual

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -d, --debug         构建调试版本"
    echo "  -r, --release       构建发布版本（默认）"
    echo "  -i, --install       构建后自动安装到连接的设备"
    echo "  -m, --manual        仅构建，不自动安装"
    echo ""
    echo "示例:"
    echo "  $0 -r -i           构建发布版并安装"
    echo "  $0 -d -m           仅构建调试版"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--debug)
            BUILD_TYPE="debug"
            shift
            ;;
        -r|--release)
            BUILD_TYPE="release"
            shift
            ;;
        -i|--install)
            INSTALL_METHOD="adb"
            shift
            ;;
        -m|--manual)
            INSTALL_METHOD="manual"
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Flutter环境
check_flutter() {
    echo "🔍 检查Flutter环境..."
    
    if ! command -v flutter &> /dev/null; then
        echo "❌ 错误：未找到Flutter，请先安装Flutter SDK"
        exit 1
    fi
    
    echo "✅ Flutter环境检查完成"
    flutter --version
}

# 检查Android环境
check_android() {
    echo ""
    echo "🔍 检查Android环境..."
    
    if ! command -v adb &> /dev/null; then
        echo "⚠️  警告：未找到ADB，无法自动安装到设备"
        INSTALL_METHOD="manual"
    fi
    
    echo "✅ Android环境检查完成"
}

# 构建应用
build_app() {
    echo ""
    echo "🔨 开始构建应用..."
    echo "📦 构建类型: $BUILD_TYPE"
    
    # 清理之前的构建
    echo "🧹 清理之前的构建..."
    flutter clean
    flutter pub get
    
    # 构建APK
    if [ "$BUILD_TYPE" = "debug" ]; then
        echo "🔄 构建调试版APK..."
        flutter build apk --debug
        APK_PATH="build/app/outputs/flutter-apk/app-debug.apk"
    else
        echo "🔄 构建发布版APK..."
        flutter build apk --release
        APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
    fi
    
    # 检查构建结果
    if [ -f "$APK_PATH" ]; then
        echo "✅ 构建成功！"
        echo "📱 APK文件: $APK_PATH"
        
        # 显示文件信息
        APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
        echo "📊 文件大小: $APK_SIZE"
    else
        echo "❌ 构建失败！"
        exit 1
    fi
}

# 安装应用
install_app() {
    if [ "$INSTALL_METHOD" = "adb" ]; then
        echo ""
        echo "📱 开始安装应用到设备..."
        
        # 检查设备连接
        DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
        
        if [ "$DEVICES" -eq 0 ]; then
            echo "❌ 未找到连接的Android设备"
            echo "📝 请确保："
            echo "   1. 手机已连接到电脑"
            echo "   2. 已开启USB调试"
            echo "   3. 已授权此电脑的调试权限"
            echo ""
            echo "🔧 手动安装方法："
            echo "   1. 将APK文件传输到手机"
            echo "   2. 在手机上点击APK文件进行安装"
            exit 1
        fi
        
        echo "📱 发现 $DEVICES 个设备，开始安装..."
        
        # 安装APK
        if adb install -r "$APK_PATH"; then
            echo "✅ 应用安装成功！"
            echo "🎉 您现在可以在手机上使用Agent-Zero应用了"
        else
            echo "❌ 应用安装失败"
            echo "📝 请尝试手动安装APK文件"
        fi
        
    else
        echo ""
        echo "📝 手动安装说明："
        echo "   1. 将以下APK文件传输到手机："
        echo "      $APK_PATH"
        echo "   2. 在手机设置中开启'未知来源'安装"
        echo "   3. 使用文件管理器找到APK文件并点击安装"
        echo ""
    fi
}

# 显示完成信息
show_completion() {
    echo ""
    echo "🎊 === 构建完成 ==="
    echo ""
    echo "📱 应用信息："
    echo "   📦 应用名称: $APP_NAME"
    echo "   🏷️  构建类型: $BUILD_TYPE"
    echo "   📄 APK文件: $APK_PATH"
    echo ""
    
    if [ "$BUILD_TYPE" = "debug" ]; then
        echo "⚠️  注意：这是调试版本，仅用于开发测试"
    else
        echo "✅ 这是发布版本，可以正式使用"
    fi
    
    echo ""
    echo "🔧 后续步骤："
    echo "   1. 确保Agent-Zero服务正在运行"
    echo "   2. 在应用中配置正确的服务器地址"
    echo "   3. 测试与Agent-Zero的连接"
    echo ""
}

# 主执行流程
main() {
    check_flutter
    check_android
    build_app
    install_app
    show_completion
}

# 执行主流程
main

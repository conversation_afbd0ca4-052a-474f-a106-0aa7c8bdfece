#!/bin/bash

echo "=== Agent Zero Network Diagnosis ==="
echo ""

echo "1. Checking Agent Zero process..."
ps aux | grep "run_ui.py" | grep -v grep
echo ""

echo "2. Checking port 50001 listeners..."
netstat -tlnp | grep :50001
echo ""

echo "3. Checking all Python processes listening..."
netstat -tlnp | grep python
echo ""

echo "4. Checking WSL network interfaces..."
ip addr show
echo ""

echo "5. Testing localhost connection..."
curl -s -I http://localhost:50001 | head -1
echo ""

echo "6. Testing 127.0.0.1 connection..."
curl -s -I http://127.0.0.1:50001 | head -1
echo ""

echo "7. Testing WSL IP connection..."
curl -s -I http://************:50001 | head -1
echo ""

echo "8. Checking .env configuration..."
if [ -f "/mnt/e/AI/agent-zero/.env" ]; then
    grep -E "WEB_UI_HOST|WEB_UI_PORT" /mnt/e/AI/agent-zero/.env
else
    echo ".env file not found"
fi
echo ""

echo "9. Checking Agent Zero log..."
if [ -f "/tmp/agent_zero.log" ]; then
    echo "Last 10 lines of log:"
    tail -10 /tmp/agent_zero.log
else
    echo "Log file not found"
fi
echo ""

echo "10. Checking if port is in use..."
lsof -i :50001 2>/dev/null || echo "Port 50001 not in use or lsof not available"
echo ""

echo "=== Diagnosis Complete ==="
